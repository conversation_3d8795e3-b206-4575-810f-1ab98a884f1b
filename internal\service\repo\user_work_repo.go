package repo

import (
	"chongli/internal/service/dto"
	"context"

	"gorm.io/gorm"
)

type UserWorkRepo interface {
	Create(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) error
	// List 根据条件查询用户作品列表,会preload User
	List(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) ([]*dto.UserWorks, error)
	Delete(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) error
	Update(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) error
	Count(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) (int64, error)
}
