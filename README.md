# 构建依赖

构建之前请添加环境变量

- `APOLLO_META=http://192.168.1.150:8080/`
- `ENVIRONMENT=test`

# 启动命令

构建后, 启动命令如下
```bash
http_start # 启动 http 服务器
task_start # 启动定时任务
```
# 关于wire
本项目使用wire生成依赖注入代码, 请确保安装wire
```bash
go install github.com/google/wire@latest
```
根目录下运行
```bash
make wire
```


# launch.json文件
如果使用vscode, 请添加launch.json文件

```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Package",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": ["http_start"],
            "env": {
                "APOLLO_META": "http://192.168.1.150:8080/",
                "ENVIRONMENT": "test"
            }
        }
    ]
}
```

# 公用服务使用说明

## ConfigService 配置服务

ConfigService 通过 wire 自动注入，提供配置管理的公共服务。

### 主要方法

- `GetConfigByKeyFromCache(ctx, key, tx...)` - 从缓存获取配置值，缓存未命中时从数据库获取并设置缓存
- `ClearConfigCache(ctx, key)` - 清除指定配置的 Redis 缓存

## GuiyinService 归因服务

GuiyinService 提供设备绑定和渠道管理功能。

### 主要方法

- `GetAndUpdateChannelIDByDeviceID(deviceID, channel string) (int, error)` - 通过设备ID查询渠道ID
  - 检查设备绑定信息是否存在
  - 返回已绑定的渠道ID，不存在时返回0
  - 设备ID不能为空，否则返回错误

## UploadService 文件上传服务

UploadService 提供文件上传到七牛云存储的功能。

### 主要方法

- `Upload(req *dto.UploadRequestDto) (string, error)` - 上传文件到七牛云
  - 自动生成唯一文件名（基于MD5和时间戳）
  - 按日期组织文件路径格式：`YYYY/MM/DD/filename.ext`
  - 返回上传后的文件URL
  - 支持各种文件格式，自动转换扩展名为小写

## UserService 用户服务

UserService 提供用户信息管理和钻石余额操作功能。

### 主要方法

- `UpdateUser(req *dto.UpdateUserRequest) errpkg.IError` - 更新用户信息
  - 支持更新头像、用户名、手机号、删除状态
  - 检查用户存在性
  - 只更新非空字段

- `AddOrSubUserDiamondAndAddRecord(userId, diamond, mark, version, channel, op, tx...)` - 钻石余额操作
  - 支持增加钻石（op=1）和扣减钻石（op=-1）
  - 自动检查余额是否足够（扣减时）
  - 记录钻石变动历史
  - 支持事务操作，保证数据一致性
  - 自动更新用户余额并创建变动记录

## Utils 工具函数

### 版本处理函数

- `utils.VersionToVersionInt(versionStr string) int64` - 将版本字符串转换为可比较的整数
  - 使用 `hashicorp/go-version` 库，支持语义化版本控制
  - 例如：`"1.2.3"` → `1002003`，`"2.10.15"` → `2010015`
  - 支持各种版本格式，包括带 `v` 前缀的版本号
  - 解析失败时返回 `0`