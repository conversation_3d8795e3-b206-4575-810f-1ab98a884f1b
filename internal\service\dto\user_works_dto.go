package dto

import "time"

type UserWorks struct {
	ID         uint64    `json:"id"`
	UserID     uint64    `json:"user_id"`
	WorkType   string    `json:"work_type"`
	Cover      string    `json:"cover"`
	TemplateID uint64    `json:"template_id"`
	Status     int8      `json:"status"`
	ErrMsg     string    `json:"error_msg"`
	CreateAt   time.Time `json:"create_at"`
	UpdatedAt  time.Time `json:"update_at"`
}
