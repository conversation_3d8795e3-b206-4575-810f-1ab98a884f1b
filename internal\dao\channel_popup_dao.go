package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type channelPopupRepo struct {
	log *logger.Logger
	db  *gorm.DB
	rds *redis.Client
}

// NewChannelPopupRepo 创建渠道弹窗关联仓库实例
func NewChannelPopupRepo(bootStrap *component.BootStrap) repo.ChannelPopupRepo {
	return &channelPopupRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
		rds: bootStrap.Driver.GetRdsClient(),
	}
}

func (r *channelPopupRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 {
		return tx[0]
	}
	return r.db
}

// CreateChannelPopup 创建渠道弹窗关联
func (r *channelPopupRepo) CreateChannelPopup(relation *dto.ChannelPopupCreateDto) (*dto.ChannelPopupDto, error) {
	// 检查是否已存在相同的关联
	var existingRelation model.ChannelPopup
	err := r.db.Where("channel_id = ? AND popup_id = ?", relation.ChannelID, relation.PopupID).First(&existingRelation).Error
	if err == nil {
		return nil, fmt.Errorf("渠道弹窗关联已存在")
	}
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		r.log.Error("检查渠道弹窗关联失败: %v", err)
		return nil, err
	}

	// 创建关联记录
	modelRelation := &model.ChannelPopup{
		ChannelID: relation.ChannelID,
		PopupID:   relation.PopupID,
	}

	err = r.db.Create(modelRelation).Error
	if err != nil {
		r.log.Error("创建渠道弹窗关联失败: %v", err)
		return nil, err
	}

	return &dto.ChannelPopupDto{
		ID:        modelRelation.ID,
		ChannelID: modelRelation.ChannelID,
		PopupID:   modelRelation.PopupID,
		CreateAt:  modelRelation.CreateAt,
		UpdateAt:  modelRelation.UpdateAt,
	}, nil
}

// GetExistingRelations 批量查询已存在的关联关系
func (r *channelPopupRepo) GetExistingRelations(channelIDs, popupIDs []int) ([]*dto.ChannelPopupDto, error) {
	if len(channelIDs) == 0 || len(popupIDs) == 0 {
		return []*dto.ChannelPopupDto{}, nil
	}

	var relations []model.ChannelPopup
	err := r.db.Where("channel_id IN ? AND popup_id IN ?", channelIDs, popupIDs).Find(&relations).Error
	if err != nil {
		r.log.Error("查询已存在的关联关系失败: %v", err)
		return nil, err
	}

	var results []*dto.ChannelPopupDto
	for _, rel := range relations {
		results = append(results, &dto.ChannelPopupDto{
			ID:        rel.ID,
			ChannelID: rel.ChannelID,
			PopupID:   rel.PopupID,
			CreateAt:  rel.CreateAt,
			UpdateAt:  rel.UpdateAt,
		})
	}

	return results, nil
}

// BatchCreateRelations 批量创建关联关系（不检查重复）
func (r *channelPopupRepo) BatchCreateRelations(relations []dto.ChannelPopupCreateDto) ([]*dto.ChannelPopupDto, error) {
	if len(relations) == 0 {
		return []*dto.ChannelPopupDto{}, nil
	}

	var modelRelations []model.ChannelPopup
	for _, rel := range relations {
		modelRelations = append(modelRelations, model.ChannelPopup{
			ChannelID: rel.ChannelID,
			PopupID:   rel.PopupID,
		})
	}

	err := r.db.Create(&modelRelations).Error
	if err != nil {
		r.log.Error("批量创建关联关系失败: %v", err)
		return nil, err
	}

	var results []*dto.ChannelPopupDto
	for _, rel := range modelRelations {
		results = append(results, &dto.ChannelPopupDto{
			ID:        rel.ID,
			ChannelID: rel.ChannelID,
			PopupID:   rel.PopupID,
			CreateAt:  rel.CreateAt,
			UpdateAt:  rel.UpdateAt,
		})
	}

	return results, nil
}

// DeleteChannelPopup 删除渠道弹窗关联（硬删除）
func (r *channelPopupRepo) DeleteChannelPopup(id int) error {
	err := r.db.Delete(&model.ChannelPopup{}, id).Error
	if err != nil {
		r.log.Error("删除渠道弹窗关联失败: %v", err)
		return err
	}
	return nil
}

// DeleteChannelPopupByIds 批量删除渠道弹窗关联（硬删除）
func (r *channelPopupRepo) DeleteChannelPopupByIds(ids []int) error {
	if len(ids) == 0 {
		return nil
	}

	err := r.db.Where("id IN ?", ids).Delete(&model.ChannelPopup{}).Error
	if err != nil {
		r.log.Error("批量删除渠道弹窗关联失败: %v", err)
		return err
	}
	return nil
}

// DeleteChannelPopupByChannelAndPopup 根据渠道ID和弹窗ID删除关联
func (r *channelPopupRepo) DeleteChannelPopupByChannelAndPopup(channelID, popupID int) error {
	err := r.db.Where("channel_id = ? AND popup_id = ?", channelID, popupID).Delete(&model.ChannelPopup{}).Error
	if err != nil {
		r.log.Error("删除渠道弹窗关联失败: %v", err)
		return err
	}
	return nil
}

// GetChannelPopupWithDetails 获取带详细信息的渠道弹窗关联列表（通用查询）
func (r *channelPopupRepo) GetChannelPopupWithDetails(query *dto.ChannelPopupQueryDto) ([]*dto.ChannelPopupDto, error) {
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 {
		query.PageSize = 10
	}

	// 获取关联关系数据
	var relations []struct {
		ID        int       `gorm:"column:id"`
		ChannelID int       `gorm:"column:channel_id"`
		PopupID   int       `gorm:"column:popup_id"`
		CreateAt  time.Time `gorm:"column:create_at"`
		UpdateAt  time.Time `gorm:"column:update_at"`
	}

	queryBuilder := r.db.Table("channel_popup cp").
		Select("cp.*").
		Joins("LEFT JOIN channel c ON cp.channel_id = c.id").
		Joins("LEFT JOIN popup p ON cp.popup_id = p.id")

	queryBuilder = r.buildQueryConditionWithDetails(queryBuilder, query)

	offset := (query.Page - 1) * query.PageSize
	err := queryBuilder.Offset(offset).Limit(query.PageSize).Order("cp.id DESC").Find(&relations).Error
	if err != nil {
		r.log.Error("查询渠道弹窗关联列表失败: %v", err)
		return nil, err
	}

	// 获取渠道ID和弹窗ID列表
	channelIDs := make([]int, 0, len(relations))
	popupIDs := make([]int, 0, len(relations))
	for _, rel := range relations {
		channelIDs = append(channelIDs, rel.ChannelID)
		popupIDs = append(popupIDs, rel.PopupID)
	}

	// 批量查询渠道信息
	var channels []model.MarketingChannel
	channelMap := make(map[int]*dto.MarketingChannelDto)
	if len(channelIDs) > 0 {
		err = r.db.Where("id IN ? ", channelIDs).Find(&channels).Error
		if err != nil {
			r.log.Error("查询渠道信息失败: %v", err)
			return nil, err
		}
		for _, c := range channels {
			channelMap[c.ID] = &dto.MarketingChannelDto{
				ID:        c.ID,
				Type:      c.Type,
				Title:     c.Title,
				BindKey:   c.BindKey,
				BindValue: c.BindValue,
				IsDeleted: c.IsDeleted,
				CreateAt:  c.CreateAt,
				UpdateAt:  c.UpdateAt,
			}
		}
	}

	// 批量查询弹窗信息
	var popups []model.PopupModel
	popupMap := make(map[int]*dto.PopupDto)
	if len(popupIDs) > 0 {
		err = r.db.Where("id IN ?  ", popupIDs).Find(&popups).Error
		if err != nil {
			r.log.Error("查询弹窗信息失败: %v", err)
			return nil, err
		}
		for _, p := range popups {
			popupMap[p.ID] = &dto.PopupDto{
				ID:         p.ID,
				Location:   p.Location,
				Jump:       p.Jump,
				JumpParam:  p.JumpParam,
				PopupImg:   p.PopupImg,
				PopupVideo: p.PopupVideo,
				Title:      p.Title,
				Content:    p.Content,
				IsDelete:   p.IsDelete,
				CreateAt:   p.CreateAt,
				UpdateAt:   p.UpdateAt,
			}
		}
	}

	// 组装结果
	var results []*dto.ChannelPopupDto
	for _, rel := range relations {
		result := &dto.ChannelPopupDto{
			ID:        rel.ID,
			ChannelID: rel.ChannelID,
			PopupID:   rel.PopupID,
			CreateAt:  rel.CreateAt,
			UpdateAt:  rel.UpdateAt,
			Channel:   channelMap[rel.ChannelID],
			Popup:     popupMap[rel.PopupID],
		}
		results = append(results, result)
	}

	return results, nil
}

// GetChannelPopupCount 获取渠道弹窗关联总数（三表连查）
func (r *channelPopupRepo) GetChannelPopupCount(query *dto.ChannelPopupQueryDto) (int64, error) {
	// 构建三表连查的查询
	queryBuilder := r.db.Table("channel_popup cp").
		Joins("LEFT JOIN channel c ON cp.channel_id = c.id").
		Joins("LEFT JOIN popup p ON cp.popup_id = p.id")

	// 添加查询条件
	queryBuilder = r.buildQueryConditionWithDetails(queryBuilder, query)

	// 获取总数
	var total int64
	err := queryBuilder.Count(&total).Error
	if err != nil {
		r.log.Error("查询渠道弹窗关联总数失败: %v", err)
		return 0, err
	}

	return total, nil
}

// buildQueryConditionWithDetails 构建带详细信息的查询条件
func (r *channelPopupRepo) buildQueryConditionWithDetails(query *gorm.DB, queryDto *dto.ChannelPopupQueryDto) *gorm.DB {
	if queryDto.ChannelID > 0 {
		query = query.Where("cp.channel_id = ?", queryDto.ChannelID)
	}

	if queryDto.PopupID > 0 {
		query = query.Where("cp.popup_id = ?", queryDto.PopupID)
	}

	if queryDto.ChannelTitle != "" {
		query = query.Where("c.title LIKE ?", "%"+queryDto.ChannelTitle+"%")
	}

	if queryDto.PopupTitle != "" {
		query = query.Where("p.title LIKE ?", "%"+queryDto.PopupTitle+"%")
	}

	if queryDto.ChannelType != nil {
		query = query.Where("c.type = ?", *queryDto.ChannelType)
	}

	if queryDto.PopupLocation != "" {
		query = query.Where("p.location = ?", queryDto.PopupLocation)
	}

	// 只有传入-1或1的时候才添加渠道删除状态条件
	if queryDto.ChannelIsDeleted != nil && (*queryDto.ChannelIsDeleted == -1 || *queryDto.ChannelIsDeleted == 1) {
		query = query.Where("c.is_deleted = ?", *queryDto.ChannelIsDeleted)
	}

	// 只有传入-1或1的时候才添加弹窗删除状态条件
	if queryDto.PopupIsDeleted != nil && (*queryDto.PopupIsDeleted == -1 || *queryDto.PopupIsDeleted == 1) {
		query = query.Where("p.is_delete = ?", *queryDto.PopupIsDeleted)
	}

	return query
}
