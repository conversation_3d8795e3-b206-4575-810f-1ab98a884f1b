package repo

import (
	"context"

	"chongli/internal/service/dto"
	"gorm.io/gorm"
)

type TaskStepRepo interface {
	// Create 创建任务步骤，可选传入事务
	Create(ctx context.Context, step *dto.TaskStepDTO, tx ...*gorm.DB) error

	// CreateBatch 批量创建任务步骤，可选传入事务
	CreateBatch(ctx context.Context, steps []*dto.TaskStepDTO, tx ...*gorm.DB) error

	// Update 更新任务步骤，可选传入事务
	Update(ctx context.Context, id int64, updates map[string]interface{}, tx ...*gorm.DB) error

	// Delete 删除任务步骤，可选传入事务
	Delete(ctx context.Context, id int64, tx ...*gorm.DB) error

	// DeleteByTaskID 根据任务ID删除所有步骤，可选传入事务
	DeleteByTaskID(ctx context.Context, taskID int64, tx ...*gorm.DB) error

	// GetStepWithTask 根据查询条件获取步骤信息，可选传入事务
	GetStepWithTask(ctx context.Context, query *dto.GetStepWithTaskQuery, tx ...*gorm.DB) ([]*dto.TaskStepDTO, error)
}
