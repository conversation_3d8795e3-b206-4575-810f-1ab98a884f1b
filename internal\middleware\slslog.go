package middleware

import (
	"bytes"
	"encoding/json"
	"io"
	"net/url"
	"strings"
	"time"

	"chongli/pkg/aliyun"
	"github.com/ahmetb/go-linq/v3"
	"github.com/gin-gonic/gin"
)

// pathsWithNoSLSLog 不需要打接口请求和响应日志的接口路径.
var pathsWithNoSLSLog = []string{
	"/api/admin/sls/log/query",
	"/api/admin/page/log",
}

// SlsLog sls log.
func SlsLog() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		betTime := time.Now().UnixMilli()
		xRequestID := ctx.GetString("X-Request-Id")

		URL, _ := url.Parse(ctx.Request.RequestURI)

		if linq.From(pathsWithNoSLSLog).Contains(URL.Path) {
			ctx.Next()
			return
		}

		body, _ := ctx.GetRawData()
		bodyStr := ""
		//判断请求是文件
		if !strings.HasPrefix(ctx.Request.Header.Get("Content-Type"), "multipart/form-data") {
			bodyStr = string(body)
		} else {
			bodyStr = ""
		}

		bodyStr = strings.ReplaceAll(strings.ReplaceAll(bodyStr, "\r", ""), "\n", "")

		requestLog := RequestLog{RequestURI: ctx.Request.RequestURI, RequestBody: bodyStr}
		requestBytes, _ := json.Marshal(requestLog)
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(body))
		aliyun.SLSWithRequestID(xRequestID, URL.Path, string(requestBytes))

		// write back.
		blw := &CustomResponseWriter{body: bytes.NewBufferString(""), ResponseWriter: ctx.Writer}
		ctx.Writer = blw

		ctx.Next()

		reason := ctx.GetString(`reason`)

		endTime := time.Now().UnixMilli()
		responseStr := strings.ReplaceAll(strings.ReplaceAll(blw.body.String(), "\r", ""), "\n", "")
		responseLog := ResponseLog{TimeConsuming: endTime - betTime, ResponseBody: responseStr, Reason: reason}
		responseBytes, _ := json.Marshal(responseLog)
		aliyun.SLSWithRequestID(xRequestID, URL.Path, string(responseBytes))
	}
}

// RequestLog request log.
type RequestLog struct {
	RequestURI  string      `json:"request_uri"`
	RequestBody interface{} `json:"request_body"`
}

// ResponseLog response log.
type ResponseLog struct {
	TimeConsuming int64  `json:"time_consuming"`
	ResponseBody  string `json:"response_body"`
	Reason        string `json:"reason"`
}

// CustomResponseWriter custom response writer.
type CustomResponseWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w CustomResponseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

// WriteString write string.
func (w CustomResponseWriter) WriteString(s string) (int, error) {
	w.body.WriteString(s)
	return w.ResponseWriter.WriteString(s)
}
