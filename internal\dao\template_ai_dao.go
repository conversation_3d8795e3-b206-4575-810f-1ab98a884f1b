package dao

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"

	"gorm.io/gorm"
)

// TemplateAIRepoImpl AI模板仓库实现
type TemplateAIRepoImpl struct {
	db *gorm.DB
}

// NewTemplateAIRepo 创建AI模板仓库实例
func NewTemplateAIRepo(component *component.BootStrap) repo.TemplateAIRepo {
	return &TemplateAIRepoImpl{
		db: component.Driver.GetMysqlDb(),
	}
}

// Create 创建AI模板
func (r *TemplateAIRepoImpl) Create(ctx context.Context, template *dto.TemplateAIDTO, tx ...*gorm.DB) error {
	db := r.getDB(tx...)

	// 将DTO转换为Model
	aiTemplate := r.dtoToModel(template)

	// 创建记录
	result := db.Create(aiTemplate)
	if result.Error != nil {
		return fmt.Errorf("创建AI模板失败: %w", result.Error)
	}

	// 更新DTO的ID
	template.ID = aiTemplate.ID
	return nil
}

// Update 更新AI模板
func (r *TemplateAIRepoImpl) Update(ctx context.Context, template *dto.TemplateAIDTO, tx ...*gorm.DB) error {
	db := r.getDB(tx...)

	// 构建更新映射
	updates := map[string]interface{}{
		"name":            template.Name,
		"cover_url":       template.CoverURL,
		"video_cover_url": template.VideoCoverURL,
		"status":          template.Status,
		"sort_order":      template.SortOrder,
		"category":        template.CategoryID,
		"variables_json":  template.VariablesJSON,
		"description":     template.Description,
	}

	// 执行更新
	result := db.Model(&model.AITemplate{}).Where("id = ?", template.ID).Updates(updates)
	if result.Error != nil {
		return fmt.Errorf("更新AI模板失败: %w", result.Error)
	}

	// 检查是否找到记录
	if result.RowsAffected == 0 {
		return fmt.Errorf("未找到ID为%d的AI模板", template.ID)
	}

	return nil
}

// Delete 删除AI模板
func (r *TemplateAIRepoImpl) Delete(ctx context.Context, id uint64, tx ...*gorm.DB) error {
	db := r.getDB(tx...)

	// 执行删除
	result := db.Delete(&model.AITemplate{}, id)
	if result.Error != nil {
		return fmt.Errorf("删除AI模板失败: %w", result.Error)
	}

	// 检查是否找到记录
	if result.RowsAffected == 0 {
		return fmt.Errorf("未找到ID为%d的AI模板", id)
	}

	return nil
}

// GetByID 根据ID获取AI模板，支持联表查询模板分类
func (r *TemplateAIRepoImpl) GetByID(ctx context.Context, id uint64, tx ...*gorm.DB) (*dto.TemplateAIDTO, error) {
	db := r.getDB(tx...)

	var template model.AITemplate
	result := db.Preload("TemplateCategory").Where("id = ?", id).First(&template)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("未找到ID为%d的AI模板", id)
		}
		return nil, fmt.Errorf("查询AI模板失败: %w", result.Error)
	}

	return r.modelToDTO(&template), nil
}

// GetByQuery 根据条件查询单个AI模板，支持联表查询模板分类
func (r *TemplateAIRepoImpl) GetByQuery(ctx context.Context, query *dto.TemplateAIQueryDTO, tx ...*gorm.DB) (*dto.TemplateAIQueryDTO, error) {
	db := r.getDB(tx...)

	// 构建查询
	db = r.buildQuery(db, query)

	var template model.AITemplate
	result := db.First(&template)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // 未找到记录，返回nil
		}
		return nil, fmt.Errorf("查询AI模板失败: %w", result.Error)
	}

	// 将查询结果转换为DTO
	templateDTO := r.modelToDTO(&template)

	// 构建返回结果
	resultQuery := &dto.TemplateAIQueryDTO{
		Template:       *templateDTO,
		PageNum:        query.PageNum,
		PageSize:       query.PageSize,
		OrderBy:        query.OrderBy,
		OrderDirection: query.OrderDirection,
	}

	// 如果有关联的模板分类，也设置到结果中
	if template.TemplateCategory != nil {
		resultQuery.Category = r.categoryToDTO(template.TemplateCategory)
	}

	return resultQuery, nil
}

// buildQueryConditions 构建共用的查询条件
func (r *TemplateAIRepoImpl) buildQueryConditions(db *gorm.DB, query *dto.TemplateAIQueryDTO) *gorm.DB {
	// 预加载模板分类
	db = db.Preload("TemplateCategory")

	// 添加AI模板的查询条件
	if query.Template.ID > 0 {
		db = db.Where("ai_template.id = ?", query.Template.ID)
	}
	if query.Template.Name != "" {
		db = db.Where("ai_template.name LIKE ?", "%"+query.Template.Name+"%")
	}
	if query.Template.CategoryID > 0 {
		db = db.Where("ai_template.category_id = ?", query.Template.CategoryID)
	}
	if query.Template.Status != 0 {
		db = db.Where("ai_template.status = ?", query.Template.Status)
	}
	if query.Template.MaxVersionInt > 0 {
		db = db.Where("ai_template.max_version_int <= ?", query.Template.MaxVersionInt)
	}

	// 构建连接查询
	db = db.Joins("LEFT JOIN template_category ON ai_template.category_id = template_category.id")

	// 添加模板分类的查询条件
	if query.Category.ID > 0 {
		db = db.Where("template_category.id = ?", query.Category.ID)
	}
	if query.Category.IsActive != 0 {
		db = db.Where("template_category.is_active = ?", query.Category.IsActive)
	}
	if query.Category.IsDelete != 0 {
		db = db.Where("template_category.is_delete = ?", query.Category.IsDelete)
	}
	if query.Category.MainClass != 0 {
		db = db.Where("template_category.main_class = ?", query.Category.MainClass)
	}

	return db
}

// ListByQuery 根据条件查询AI模板列表，支持联表查询模板分类
func (r *TemplateAIRepoImpl) ListByQuery(ctx context.Context, query *dto.TemplateAIQueryDTO, tx ...*gorm.DB) ([]*dto.TemplateAIQueryDTO, error) {
	db := r.getDB(tx...)

	// 构建查询条件
	db = r.buildQueryConditions(db, query)

	// 分页处理
	var templates []model.AITemplate
	if query.PageNum > 0 && query.PageSize > 0 {
		offset := (query.PageNum - 1) * query.PageSize
		db = db.Offset(offset).Limit(query.PageSize)
	}

	// 添加排序
	if query.OrderBy != "" {
		direction := "ASC"
		if query.OrderDirection == "desc" {
			direction = "DESC"
		}

		db = db.Order(fmt.Sprintf("%s %s", query.OrderBy, direction))
	} else {
		// 默认按ID降序排序
		db = db.Order("ai_template.id DESC")
	}

	// 执行查询
	err := db.Find(&templates).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("查询AI模板列表失败: %w", err)
	}

	// 转换为DTO
	dtos := make([]*dto.TemplateAIQueryDTO, len(templates))
	for i, template := range templates {
		templateDTO := r.modelToDTO(&template)
		dtos[i] = &dto.TemplateAIQueryDTO{
			Template:       *templateDTO,
			PageNum:        query.PageNum,
			PageSize:       query.PageSize,
			OrderBy:        query.OrderBy,
			OrderDirection: query.OrderDirection,
		}

		// 如果有关联的模板分类，也设置到结果中
		if template.TemplateCategory != nil {
			dtos[i].Category = r.categoryToDTO(template.TemplateCategory)
		}
	}

	return dtos, nil
}

// ListAll 查询所有AI模板，支持联表查询模板分类
func (r *TemplateAIRepoImpl) ListAll(ctx context.Context, tx ...*gorm.DB) ([]*dto.TemplateAIDTO, error) {
	db := r.getDB(tx...)

	var templates []model.AITemplate
	result := db.Preload("TemplateCategory").Find(&templates)
	if result.Error != nil {
		return nil, fmt.Errorf("查询所有AI模板失败: %w", result.Error)
	}

	// 转换为DTO
	dtos := make([]*dto.TemplateAIDTO, len(templates))
	for i, template := range templates {
		dtos[i] = r.modelToDTO(&template)
	}

	return dtos, nil
}

// Count 根据条件统计AI模板数量
func (r *TemplateAIRepoImpl) Count(ctx context.Context, query *dto.TemplateAIQueryDTO, tx ...*gorm.DB) (int64, error) {
	db := r.getDB(tx...)

	// 构建查询条件
	db = r.buildQueryConditions(db, query)

	// 执行统计查询
	var count int64
	if err := db.Model(&model.AITemplate{}).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("统计AI模板数量失败: %w", err)
	}

	return count, nil
}

// buildQuery 构建查询条件
func (r *TemplateAIRepoImpl) buildQuery(db *gorm.DB, query *dto.TemplateAIQueryDTO) *gorm.DB {
	// 预加载模板分类
	db = db.Preload("TemplateCategory")

	// 添加AI模板的查询条件
	if query.Template.ID > 0 {
		db = db.Where("ai_template.id = ?", query.Template.ID)
	}
	if query.Template.Name != "" {
		db = db.Where("ai_template.name LIKE ?", "%"+query.Template.Name+"%")
	}
	if query.Template.CategoryID > 0 {
		db = db.Where("ai_template.category = ?", query.Template.CategoryID)
	}
	if query.Template.Status != 0 {
		db = db.Where("ai_template.status = ?", query.Template.Status)
	}
	if query.Template.MaxVersionInt > 0 {
		db = db.Where("ai_template.max_version_int <= ?", query.Template.MaxVersionInt)
	}

	// 添加模板分类的查询条件（联表查询）
	db = db.Joins("LEFT JOIN template_category ON ai_template.category = template_category.id")

	if query.Category.ID > 0 {
		db = db.Where("template_category.id = ?", query.Category.ID)
	}
	if query.Category.IsActive != 0 {
		db = db.Where("template_category.is_active = ?", query.Category.IsActive)
	}
	if query.Category.IsDelete != 0 {
		db = db.Where("template_category.is_delete = ?", query.Category.IsDelete)
	}
	if query.Category.MainClass != 0 {
		db = db.Where("template_category.main_class = ?", query.Category.MainClass)
	}

	return db
}

// modelToDTO 将模型转换为DTO
func (r *TemplateAIRepoImpl) modelToDTO(model *model.AITemplate) *dto.TemplateAIDTO {
	if model == nil {
		return nil
	}

	// 创建DTO并设置基本字段
	result := &dto.TemplateAIDTO{
		ID:            model.ID,
		Name:          model.Name,
		CoverURL:      model.CoverURL,
		VideoCoverURL: model.VideoCoverURL,
		// 将int8类型转换为StatusFlag类型
		Status:        model.Status,
		SortOrder:     model.SortOrder,
		MaxVersionInt: model.MaxVersionInt,
		CategoryID:    model.CategoryID,
		Description:   model.Description,
		CreateAt:      model.CreateAt,
		UpdatedAt:     model.UpdatedAt,
		DiamondCost:   model.DiamondCost,
		Template:      string(*model.VariablesJSON),
	}

	// 处理VariablesJSON
	if model.VariablesJSON != nil {
		var jsonValue json.RawMessage = *model.VariablesJSON
		result.VariablesJSON = &jsonValue
	}

	// 处理分类
	if model.TemplateCategory != nil {
		result.Category = r.categoryToDTO(model.TemplateCategory)
	}

	return result
}

// categoryToDTO 将模板分类模型转换为DTO
func (r *TemplateAIRepoImpl) categoryToDTO(category *model.TemplateCategory) dto.TemplateCategoryDto {
	if category == nil {
		return dto.TemplateCategoryDto{}
	}

	return dto.TemplateCategoryDto{
		ID:            category.ID,
		Name:          category.Name,
		Sort:          category.Sort,
		MaxVersion:    category.MaxVersion,
		MaxVersionInt: category.MaxVersionInt,
		IsActive:      category.IsActive,
		IsDelete:      category.IsDelete,
		CreateAt:      category.CreateAt,
		UpdateAt:      category.UpdateAt,
		MainClass:     category.MainClass,
	}
}

// dtoToModel 将DTO转换为模型
func (r *TemplateAIRepoImpl) dtoToModel(templateDTO *dto.TemplateAIDTO) *model.AITemplate {
	if templateDTO == nil {
		return nil
	}

	return &model.AITemplate{
		ID:            templateDTO.ID,
		Name:          templateDTO.Name,
		CoverURL:      templateDTO.CoverURL,
		VideoCoverURL: templateDTO.VideoCoverURL,
		Status:        int8(templateDTO.Status),
		SortOrder:     templateDTO.SortOrder,
		CategoryID:    templateDTO.CategoryID,
		VariablesJSON: templateDTO.VariablesJSON,
		Description:   templateDTO.Description,
		CreateAt:      templateDTO.CreateAt,
		UpdatedAt:     templateDTO.UpdatedAt,
		DiamondCost:   templateDTO.DiamondCost,
	}
}

// getDB 获取数据库连接
func (r *TemplateAIRepoImpl) getDB(tx ...*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return r.db
}
