package repo

import (
	"chongli/internal/service/dto"
	"context"

	"gorm.io/gorm"
)

type TaskRepo interface {
	// Create 创建任务，可选传入事务
	Create(ctx context.Context, task *dto.TaskDTO, tx ...*gorm.DB) (*dto.TaskDTO, error)

	// GetByID 根据ID获取任务，可选传入事务
	GetByID(ctx context.Context, id int64, tx ...*gorm.DB) (*dto.TaskDTO, error)

	// Update 更新任务，可选传入事务
	Update(ctx context.Context, id int64, updates map[string]interface{}, tx ...*gorm.DB) error

	// Delete 删除任务，可选传入事务
	Delete(ctx context.Context, id int64, tx ...*gorm.DB) error
}
