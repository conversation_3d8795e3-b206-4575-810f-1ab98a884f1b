package component

import (
	"chongli/component/apollo"
	"chongli/component/driver"
	"chongli/pkg/email"
	"chongli/pkg/logger"
	"github.com/panjf2000/ants/v2"
)

type BootStrap struct {
	Config        *apollo.Config
	Log           *logger.Logger
	Driver        driver.IDriverContainer
	Tx            driver.ITransaction
	GoroutinePool *ants.Pool
	// ....
}

// NewBootStrap 创建并初始化 BootStrap
func NewBootStrap() *BootStrap {
	// 初始化日志
	log := logger.Log()

	// 初始化 Apollo 配置
	apollo.InitApolloConfigs()
	config := apollo.GetApolloConfig()

	// 初始化邮件服务
	email.InitEmailServer()

	// 初始化驱动容器
	driverContainer := driver.InitDrivers()

	// 初始化事务
	transaction := driver.InitTransaction()

	antsPool, err := ants.NewPool(200)
	if err != nil {
		logger.Log().Error("创建协程池失败: %v", err)
		panic(err)
	}

	return &BootStrap{
		Config:        config,
		Log:           log,
		Driver:        driverContainer,
		Tx:            transaction,
		GoroutinePool: antsPool,
	}
}
