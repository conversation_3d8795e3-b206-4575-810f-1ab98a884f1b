package service

import (
	"chongli/component"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"context"
	"encoding/json"
	"errors"

	"gorm.io/gorm"
)

type TemplateService struct {
	templateRepo repo.TemplateAIRepo
	log          *logger.Logger
}

func NewTemplateService(templateRepo repo.TemplateAIRepo, component *component.BootStrap) *TemplateService {
	return &TemplateService{
		templateRepo: templateRepo,
		log:          component.Log,
	}
}

func (s *TemplateService) GetAiTemplateById(ctx context.Context, templateId uint64, tx *gorm.DB) (*dto.TemplateAIDTO, *dto.AIPicTemplate, error) {
	templateInfo, err := s.templateRepo.GetByID(ctx, templateId, tx)
	if err != nil {
		return nil, nil, err
	}
	if templateInfo == nil || templateInfo.ID <= 0 {
		return nil, nil, errors.New("模板不存在")
	}
	if templateInfo.Status != 1 {
		return nil, nil, errors.New("模板已下架")
	}
	templateParam := dto.AIPicTemplate{}
	err = json.Unmarshal([]byte(templateInfo.Template), &templateParam)
	if err != nil {
		s.log.Error("模板参数解析失败: %v", err)
		return nil, nil, errors.New("模板参数解析失败")
	}
	return templateInfo, &templateParam, nil
}
