package biz

import (
	"chongli/component"
	"chongli/component/driver"
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"chongli/pkg/utils"
	"context"
	"encoding/json"
	"errors"
)

type UserWorkBiz struct {
	userWorkRepo    repo.UserWorkRepo
	userService     *service.UserService
	taskService     *service.TaskService
	taskStepRepo    repo.TaskStepRepo
	tx              driver.ITransaction
	templateService *service.TemplateService
	log             *logger.Logger
}

type CreatePicWorksRequest struct {
	TemplateID uint64 `json:"template_id" binding:"required"`
	PersonPic  string `json:"person_pic" binding:"required"`
	PetPic     string `json:"pet_pic" binding:"required"`
	UserID     int64  `json:"user_id"`
	*utils.RequestHeaderDto
}

func NewUserWorkBiz(
	userWorkRepo repo.UserWorkRepo,
	taskService *service.TaskService,
	taskStepRepo repo.TaskStepRepo,
	bootStrap *component.BootStrap,
	userService *service.UserService,
	templateService *service.TemplateService) *UserWorkBiz {
	return &UserWorkBiz{
		userWorkRepo:    userWorkRepo,
		userService:     userService,
		taskService:     taskService,
		taskStepRepo:    taskStepRepo,
		tx:              bootStrap.Tx,
		templateService: templateService,
		log:             bootStrap.Log,
	}
}

func (b *UserWorkBiz) CreatePicWorks(ctx context.Context, req *CreatePicWorksRequest) (err error) {
	tx := b.tx.MysqlDbTxBegin()
	if tx.Error != nil {
		return tx.Error
	}

	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			err = r.(error)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit().Error
		}
	}()
	templateInfo, templateParam, err := b.templateService.GetAiTemplateById(ctx, req.TemplateID, tx)
	if err != nil {
		return err
	}
	if templateInfo == nil || templateInfo.ID <= 0 {
		return errors.New("模板不存在")
	}
	if templateInfo.Status != 1 {
		return errors.New("模板已下架")
	}
	// 1. 扣减钻石
	err = b.userService.AddOrSubUserDiamondAndAddRecord(service.DiamondOperationRequest{
		UserID:  req.UserID,
		Diamond: uint64(templateInfo.DiamondCost),
		Mark:    "写真扣减钻石",
		Version: req.Version,
		Channel: req.Channel,
		Op:      int(model.StatusDisabled),
	}, tx)
	if err != nil {
		return err
	}

	// 2. 写入作品数据
	userWork := &dto.UserWorks{
		UserID:     uint64(req.UserID),
		TemplateID: req.TemplateID,
		WorkType:   "pic",
	}
	err = b.userWorkRepo.Create(ctx, userWork, tx)
	if err != nil {
		b.log.Error("写入作品数据失败: %v", err)
		return err
	}
	task := dto.TaskDTO{
		TaskType: "pic_work",
		UserID:   req.UserID,
		Status:   model.StepStatusPending,
		DeviceID: req.DeviceId,
	}
	taskAll := &dto.CreateTaskRequest{}
	taskAll.TaskDTO = task

	taskStepOne := dto.TaskStepDTO{
		StepIndex: 0,
		StepName:  "ai_compound",
		Status:    model.StepStatusPending,
		Params: dto.JSONMap{
			"pet_pic":          req.PetPic,
			"pet_desc":         templateParam.PetDesc,
			"background_desc":  templateParam.BackgroundDesc,
			"composition_desc": templateParam.CompositionDesc,
			"style":            templateParam.Style,
			"strength":         templateParam.ModelStrength,
		},
		RetryCount: 0,
	}

	taskAll.Steps = append(taskAll.Steps, taskStepOne)
	taskStepTwo := dto.TaskStepDTO{
		StepIndex: 1,
		StepName:  "change_style",
		Status:    model.StepStatusInit,
		Params: dto.JSONMap{
			"style_desc": templateParam.StyleDesc,
		},
		RetryCount: 0,
	}
	taskAll.Steps = append(taskAll.Steps, taskStepTwo)

	stepThree := dto.TaskStepDTO{
		StepIndex:  2,
		StepName:   "change_face",
		Status:     model.StepStatusInit,
		Params:     dto.JSONMap{},
		RetryCount: 0,
	}
	taskAll.Steps = append(taskAll.Steps, stepThree)
	taskData, err := json.Marshal(taskAll)
	if err != nil {
		return err
	}

	var taskModel dto.TaskDTO
	err = json.Unmarshal(taskData, &taskModel)
	if err != nil {
		return err
	}

	_, err = b.taskService.CreateTaskWithSteps(ctx, taskAll, tx)
	if err != nil {
		return err
	}

	return nil
}
