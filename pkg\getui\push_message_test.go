package getui

import (
	"chongli/pkg/utils"
	"testing"
	"time"
)

func TestPushSingleByCid(t *testing.T) {
	requestId := utils.CreateUUid()
	cid := "5abc69bd11e5a937c67c30616dd076ec"
	msg := &Transmission{
		Type: MakeSong,
		Payload: MakeSongPayload{
			SongID:   1,
			State:    1,
			ErrMsg:   "test err msg",
			Title:    "test title",
			ImgURL:   "test img url",
			CreateAt: time.Now().UnixMilli(),
		},
		Msg:  "歌曲制作成功",
		Time: time.Now().UnixMilli(),
	}
	ttl := 3000
	respData, err := PushMsgByCid(requestId, cid, msg, ttl)
	if err != nil {
		t.Error(err)
	} else {
		t.Log(respData)
	}
}

func TestPushMsgByCid(t *testing.T) {
	requestId := utils.CreateUUid()
	cid := "5abc69bd11e5a937c67c30616dd076ec"
	msg := &Transmission{
		Type: MakeSong,
		Payload: MakeSongPayload{
			SongID:   1,
			State:    1,
			ErrMsg:   "test err msg",
			Title:    "test title",
			ImgURL:   "test img url",
			CreateAt: time.Now().UnixMilli(),
		},
		Msg:  "歌曲制作成功",
		Time: time.Now().UnixMilli(),
	}
	ttl := 3000
	respData, err := PushMsgByCid(requestId, cid, msg, ttl)
	if err != nil {
		t.Error(err)
	} else {
		t.Log(respData)
	}
}

func TestPushMsgByCidMv(t *testing.T) {
	requestId := utils.CreateUUid()
	cid := "a9b719c79e61a86ed3ae563ae7892cce"
	msg := &Transmission{
		Type: MakeMv,
		Payload: MakeMvPayload{
			MvID:     1,
			State:    1,
			ErrMsg:   "test err msg",
			Title:    "test title",
			ImgURL:   "test img url",
			CreateAt: time.Now().UnixMilli(),
		},
		Msg:  "mv制作成功",
		Time: time.Now().UnixMilli(),
	}
	ttl := 3000
	respData, err := PushMsgByCid(requestId, cid, msg, ttl)
	if err != nil {
		t.Error(err)
	} else {
		t.Log(respData)
	}
}
