package model

import "time"

type GoodsVip struct {
	Id             int       `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                   // 主键id
	GoodsId        string    `gorm:"column:goods_id;NOT NULL" json:"goods_id"`                         // 商品id
	Title          string    `gorm:"column:title;NOT NULL" json:"title"`                               // 商品名称
	Price          float64   `gorm:"column:price;default:0.00;NOT NULL" json:"price"`                  // 商品价格
	VipType        int       `gorm:"column:vip_type;default:0;NOT NULL" json:"vip_type"`               // vip类型;1:月;2:季;3:年
	Version        string    `gorm:"column:version;NOT NULL" json:"version"`                           // 创建版本
	Channel        string    `gorm:"column:channel;NOT NULL" json:"channel"`                           // 客户端/渠道
	Sort           int       `gorm:"column:sort;default:0;NOT NULL" json:"sort"`                       // 排序;升序排列
	CreateAt       time.Time `gorm:"column:create_at;NOT NULL;autoCreateTime" json:"create_at"`        // 创建时间
	UpdateAt       time.Time `gorm:"column:update_at;NOT NULL;autoUpdateTime" json:"update_at"`        // 更新时间
	IsDelete       int8      `gorm:"column:is_delete;default:0;NOT NULL" json:"is_delete"`             // 是否已被删除，0：未删除；1：已删除
	MonthlyDiamond int       `gorm:"column:monthly_diamond;default:0;NOT NULL" json:"monthly_diamond"` // 月钻石数量
	IsDisplay      int8      `gorm:"column:is_display;default:1;NOT NULL" json:"is_display"`           // 是否显示，-1：否；1：是
}

func (m *GoodsVip) TableName() string {
	return "goods_vip"
}
