package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

const (
	SendCoolingTime = 1 * time.Minute
	errorKey        = "phone:error:"
)

type userRepo struct {
	log *logger.Logger
	db  *gorm.DB
	rds *redis.Client
}

func NewUserRepo(bootStrap *component.BootStrap) repo.UserRepo {
	return &userRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
		rds: bootStrap.Driver.GetRdsClient(),
	}
}

func (d *userRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

// UserWithChannelInfo 用户信息与渠道信息的联合查询结果
type UserWithChannelInfo struct {
	model.User
	UserBindChannelId   int64  `gorm:"column:user_bind_channel_id"`   // 用户绑定渠道id
	UserBindChannelName string `gorm:"column:user_bind_channel_name"` // 用户绑定渠道名称
}

func (d *userRepo) convertModel2Dto(user *model.User) *dto.UserInfoDto {
	return &dto.UserInfoDto{
		ID:              user.ID,
		DeviceId:        user.DeviceId,
		Avatar:          user.Avatar,
		Username:        user.Username,
		Phone:           user.Phone,
		Diamond:         user.Diamond,
		IsVip:           user.IsVip,
		VipType:         user.VipType,
		RegisterType:    user.RegisterType,
		RegisterVersion: user.RegisterVersion,
		Channel:         user.Channel,
		Ip:              user.IP,
		IPLocation:      user.IPLocation,
		CreateAt:        user.CreateAt,
		UpdateAt:        user.UpdateAt,
		IsDelete:        user.IsDelete,
	}
}

func (d *userRepo) convertUserWithChannelInfo2Dto(userWithChannel *UserWithChannelInfo) *dto.UserInfoDto {
	return &dto.UserInfoDto{
		ID:                  userWithChannel.ID,
		DeviceId:            userWithChannel.DeviceId,
		Avatar:              userWithChannel.Avatar,
		Username:            userWithChannel.Username,
		Phone:               userWithChannel.Phone,
		Diamond:             userWithChannel.Diamond,
		IsVip:               userWithChannel.IsVip,
		VipType:             userWithChannel.VipType,
		RegisterType:        userWithChannel.RegisterType,
		RegisterVersion:     userWithChannel.RegisterVersion,
		Channel:             userWithChannel.Channel,
		Ip:                  userWithChannel.IP,
		IPLocation:          userWithChannel.IPLocation,
		CreateAt:            userWithChannel.CreateAt,
		UpdateAt:            userWithChannel.UpdateAt,
		IsDelete:            userWithChannel.IsDelete,
		UserBindChannelId:   userWithChannel.UserBindChannelId,
		UserBindChannelName: userWithChannel.UserBindChannelName,
	}
}

func (d *userRepo) DeleteUserByUserId(userId int64) error {
	if err := d.db.Model(model.User{}).Where(map[string]any{
		"id": userId,
	}).Update("is_delete", model.StatusEnabled).Error; err != nil {
		d.log.Error("删除用户错误: %v", err.Error())
		return err
	}
	return nil
}

func (d *userRepo) UncancelUserByUserId(userId int64) error {
	if err := d.db.Model(model.User{}).Where(map[string]any{
		"id": userId,
	}).Update("is_delete", model.StatusDisabled).Error; err != nil {
		d.log.Error("恢复用户错误: %v", err.Error())
		return err
	}
	return nil
}

func (d *userRepo) GetUserInfoByUserId(userId int64, tx ...*gorm.DB) (*dto.UserInfoDto, error) {
	var user model.User
	if err := d.getDb(tx).Model(model.User{}).Where(map[string]any{"id": userId}).
		Find(&user).Error; err != nil {
		d.log.Error("查询用户错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&user), nil
}

func (d *userRepo) CreateUser(user *dto.UserInfoDto) error {
	if err := d.db.Model(model.User{}).Create(user).Error; err != nil {
		d.log.Error("创建用户错误: %v", err.Error())
		return err
	}
	return nil
}

func (d *userRepo) GetUserInfoByDeviceId(deviceId string) (*dto.UserInfoDto, error) {
	var user model.User
	if err := d.db.Model(model.User{}).Where(map[string]any{
		"device_id": deviceId,
		"is_delete": model.StatusDisabled,
	}).Find(&user).Error; err != nil {
		d.log.Error("查询用户错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&user), nil
}

func (d *userRepo) UpdateUsername(userId int64, username string) error {
	if err := d.db.Model(model.User{}).Where(map[string]any{
		"id": userId,
	}).Update("username", username).Error; err != nil {
		d.log.Error("更新用户用户名错误: %v", err.Error())
		return err
	}
	return nil
}

func (d *userRepo) UpdateUserInfoByDeviceId(deviceId string, field map[string]any) error {
	if err := d.db.Model(model.User{}).Where(map[string]any{
		"device_id": deviceId,
	}).Updates(field).Error; err != nil {
		d.log.Error("更新用户信息错误: %v", err.Error())
		return err
	}
	return nil
}

func (d *userRepo) UpdateUserInfoByUserId(userId int64, field map[string]any, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(model.User{}).Where(map[string]any{
		"id": userId,
	}).Updates(field).Error; err != nil {
		d.log.Error("更新用户信息错误: %v", err.Error())
		return err
	}
	return nil
}

func (d *userRepo) GetUserInfoByField(field, data string) (*dto.UserInfoDto, error) {
	var user model.User
	if err := d.db.Model(model.User{}).Where(map[string]any{
		field: data,
	}).Find(&user).Error; err != nil {
		d.log.Error("查询用户错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&user), nil
}

func (d *userRepo) IsNewUser(field, data string) (bool, error) {
	var user model.User
	if err := d.db.Model(model.User{}).Where(map[string]any{
		field:       data,
		"is_delete": model.StatusDisabled,
	}).Find(&user).Error; err != nil {
		d.log.Error("查询用户错误: %v", err.Error())
	}
	if user.ID != 0 {
		return false, nil
	}
	return true, nil
}

func (d *userRepo) IsCancelUser(field, data string) (bool, error) {
	var user model.User
	if err := d.db.Model(model.User{}).Where(map[string]any{
		field:       data,
		"is_delete": model.StatusEnabled,
	}).Find(&user).Error; err != nil {
		d.log.Error("查询用户错误: %v", err.Error())
	}
	if user.ID != 0 {
		return true, nil
	}
	return false, nil
}

func (d *userRepo) GetPhoneError(phone string) (int64, error) {
	redisKey := errorKey + phone
	count, err := d.rds.Get(d.rds.Context(), redisKey).Int64()
	if err != nil && !errors.Is(err, redis.Nil) {
		logger.Log().Error("获取手机错误次数出错: %v", err)
		return 0, err
	}
	if errors.Is(err, redis.Nil) {
		return 0, nil
	}
	return count, nil
}

func (d *userRepo) SetPhoneError(phone string) error {
	redisKey := errorKey + phone
	err := d.rds.Incr(d.rds.Context(), redisKey).Err()
	d.rds.Expire(d.rds.Context(), redisKey, 10*time.Minute)
	if err != nil {
		logger.Log().Error("设置手机错误次数出错: %v", err)
	}
	return err
}

func (d *userRepo) SetSendCooling(phone string) error {
	hashKey := "send:cooling"
	err := d.rds.Set(d.rds.Context(), hashKey+phone, time.Now().Unix(), SendCoolingTime).Err()
	if err != nil {
		logger.Log().Error("设置发送冷却时间出错: %v", err)
	}
	return err
}

func (d *userRepo) GetSendCooling(phone string) (bool, error) {
	hashKey := "send:cooling"
	t, err := d.rds.Get(d.rds.Context(), hashKey+phone).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		logger.Log().Error("获取发送冷却时间出错: %v", err)
		return true, err
	}
	//如果不存在，则返回false，表示没有冷却时间
	if errors.Is(err, redis.Nil) {
		return false, nil
	}
	//如果存在，则判断是否过期
	if t == "" {
		return false, nil
	}
	return true, nil
}

func (d *userRepo) SetSmsCode(phone, code string) (err error) {
	hashKey := "sms:"
	err = d.rds.Set(d.rds.Context(), hashKey+phone, code, 5*time.Minute).Err()
	if err != nil {
		logger.Log().Error("保存短信验证码出错: %v %v %v", phone, code, err)
	}
	return
}

func (d *userRepo) GetSmsCode(phone string) (code string, err error) {
	redisKey := fmt.Sprintf("sms:%s", phone)
	code, err = d.rds.Get(d.rds.Context(), redisKey).Result()
	if errors.Is(err, redis.Nil) {
		return "", errors.New("验证码不存在")
	}
	if err != nil {
		logger.Log().Error("获取短信验证码出错: %v", err)
		return "", err
	}
	return code, nil
}

// QueryCondition 查询条件结构体
type QueryCondition struct {
	Field    string
	Operator string
	Value    interface{}
}

// buildUserQueryConditions 构建用户查询条件
func (d *userRepo) buildUserQueryConditions(query *gorm.DB, req *dto.GetUserListRequest) *gorm.DB {
	// 定义查询条件映射表
	conditions := []QueryCondition{
		{"u.id", "=", req.ID},
		{"u.device_id", "=", req.DeviceId},
		{"u.phone", "=", req.Phone},
		{"u.register_version", "=", req.RegisterVersion},
		{"u.channel", "=", req.Channel},
		{"gdb.channel_id", "=", req.UserBindChannelId},
		{"u.register_type", "=", req.RegisterType},
		{"u.is_delete", "=", req.IsDelete},
		{"u.is_vip", "=", req.IsVip},
		{"u.vip_type", "=", req.VipType},
	}

	// 应用基础条件
	for _, condition := range conditions {
		if d.isValidConditionValue(condition.Value) {
			query = query.Where(condition.Field+" "+condition.Operator+" ?", condition.Value)
		}
	}

	// 特殊条件处理
	if req.UserBindChannelName != "" {
		query = query.Where("c.title LIKE ?", "%"+req.UserBindChannelName+"%")
	}
	if !req.BeginCreateAt.IsZero() {
		query = query.Where("u.create_at >= ?", req.BeginCreateAt)
	}
	if !req.EndCreateAt.IsZero() {
		query = query.Where("u.create_at <= ?", req.EndCreateAt)
	}

	return query
}

// isValidConditionValue 检查条件值是否有效
func (d *userRepo) isValidConditionValue(value interface{}) bool {
	switch v := value.(type) {
	case int64:
		return v != 0
	case string:
		return v != ""
	case int8:
		return v != 0
	default:
		return false
	}
}

// buildBaseQuery 构建基础查询（包含JOIN）
func (d *userRepo) buildBaseQuery() *gorm.DB {
	return d.db.Table("user u").
		Joins("LEFT JOIN guiyin_device_bind gdb ON u.device_id = gdb.device_id").
		Joins("LEFT JOIN channel c ON gdb.channel_id = c.id AND c.is_deleted = -1")
}

// validatePageRequest 验证分页请求参数
func (d *userRepo) validatePageRequest(req *dto.GetUserListRequest) error {
	if req == nil {
		return fmt.Errorf("请求参数不能为空")
	}
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20 // 默认每页20条
	}
	if req.PageSize > 1000 {
		req.PageSize = 1000 // 最大1000条，防止查询过大数据集
	}
	return nil
}

func (d *userRepo) Page(req *dto.GetUserListRequest) ([]*dto.UserInfoDto, int64, error) {
	// 参数验证
	if err := d.validatePageRequest(req); err != nil {
		return nil, 0, err
	}

	var usersWithChannel []*UserWithChannelInfo
	var total int64

	// 构建计数查询（先执行计数，如果为0则无需查询数据）
	countQuery := d.buildUserQueryConditions(d.buildBaseQuery(), req)
	if err := countQuery.Count(&total).Error; err != nil {
		d.log.Error("查询用户总数失败: %v", err)
		return nil, 0, fmt.Errorf("查询用户总数失败: %w", err)
	}

	// 如果总数为0，直接返回空结果
	if total == 0 {
		return []*dto.UserInfoDto{}, 0, nil
	}

	// 构建数据查询
	dataQuery := d.buildUserQueryConditions(
		d.buildBaseQuery().Select("u.*, COALESCE(gdb.channel_id, 0) as user_bind_channel_id, COALESCE(c.title, '') as user_bind_channel_name"),
		req,
	)

	// 计算分页参数并获取列表数据
	offset := (req.Page - 1) * req.PageSize
	if err := dataQuery.Order("u.create_at DESC").Offset(offset).Limit(req.PageSize).Find(&usersWithChannel).Error; err != nil {
		d.log.Error("查询用户列表失败: %v", err)
		return nil, 0, fmt.Errorf("查询用户列表失败: %w", err)
	}

	// 转换为DTO（预分配切片容量以提高性能）
	dtoList := make([]*dto.UserInfoDto, 0, len(usersWithChannel))
	for _, userWithChannel := range usersWithChannel {
		dtoList = append(dtoList, d.convertUserWithChannelInfo2Dto(userWithChannel))
	}

	return dtoList, total, nil
}

func (d *userRepo) GetAllVipUsers() ([]*dto.UserInfoDto, error) {
	var users []*model.User
	err := d.db.Model(&model.User{}).Where("is_vip = ?", 1).Find(&users).Error
	if err != nil {
		d.log.Error("查询所有会员用户失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var dtoList []*dto.UserInfoDto
	for _, user := range users {
		dtoList = append(dtoList, d.convertModel2Dto(user))
	}

	return dtoList, nil
}

func (d *userRepo) BatchUpdateUserInfoByIds(ids []int64, field map[string]any) error {
	if err := d.db.Model(model.User{}).Where("id IN (?)", ids).Updates(field).Error; err != nil {
		d.log.Error("批量更新用户信息错误: %v", err.Error())
		return err
	}
	return nil
}
