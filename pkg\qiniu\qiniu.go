package qiniu

import (
	"chongli/component/apollo"
	"context"
	"fmt"
	"mime/multipart"
	"strings"

	"github.com/gookit/goutil/strutil"
	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/cdn"
	"github.com/qiniu/go-sdk/v7/storage"
)

const (
	qiNiuPrefix = "chongli/"
)

// UploadFile 上传文件到七牛云存储
func UploadFile(uid int64, file *multipart.FileHeader, fileName string) (string, error) {
	// 获取Apollo配置。
	apolloConfig := apollo.GetApolloConfig()
	// 初始化存储桶范围。
	scope := apolloConfig.QiniuyunBucket
	path := ""
	// 如果指定了文件名，则构建对应的存储路径。
	if fileName != "" {
		path = qiNiuPrefix + fileName
		scope = apolloConfig.QiniuyunBucket + ":" + path
	} else {
		// 如果未指定文件名，则生成随机文件名。
		randomChars := strutil.RandomCharsV2(24)
		fileSuffix := formatFileSuffix(file)
		if uid != 0 {
			path = fmt.Sprintf("%s%s_%d.%s", qiNiuPrefix, randomChars, uid, fileSuffix)
		} else {
			path = fmt.Sprintf("%s%s.%s", qiNiuPrefix, randomChars, fileSuffix)
		}
	}
	// 初始化上传策略。
	putPolicy := storage.PutPolicy{
		Scope: scope,
	}
	putPolicy.InsertOnly = 0
	// 创建七牛云凭证。
	mac := qbox.NewMac(apolloConfig.QiniuyunAccessKey, apolloConfig.QiniuyunSecretKey)
	// 生成上传令牌。
	upToken := putPolicy.UploadToken(mac)
	// 初始化上传配置。
	cfg := storage.Config{}
	cfg.UseHTTPS = true
	cfg.UseCdnDomains = true
	// 创建表单上传实例。
	formUploader := storage.NewFormUploader(&cfg)
	ret := storage.PutRet{}
	putExtra := storage.PutExtra{
		Params: map[string]string{},
	}
	// 打开文件以供上传。
	data, err := file.Open()
	if err != nil {
		return "", err
	}
	defer func() {
		_ = data.Close()
	}()
	// 执行上传操作。
	err = formUploader.Put(context.Background(), &ret, upToken, path, data, file.Size, &putExtra)
	if err != nil {
		return "", err
	}
	// 如果指定了文件名，则刷新CDN缓存。
	if fileName != "" {
		cdnManager := cdn.NewCdnManager(qbox.NewMac(apolloConfig.QiniuyunAccessKey, apolloConfig.QiniuyunSecretKey))
		_, _ = cdnManager.RefreshUrls([]string{fmt.Sprintf("%s/%s", apolloConfig.QiniuyunDomain, ret.Key)})
	}
	// 返回上传后的文件URL。
	return fmt.Sprintf("%s/%s", apolloConfig.QiniuyunDomain, ret.Key), nil
}

// formatFileSuffix 获取文件后缀名
func formatFileSuffix(file *multipart.FileHeader) string {
	arr := strings.Split(file.Filename, ".")
	return arr[len(arr)-1]
}
