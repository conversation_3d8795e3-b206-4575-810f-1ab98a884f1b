FROM registry.cn-qingdao.aliyuncs.com/wnlteam/wnlalpine:latest

ENV GOPATH /www/go
ENV GO111MODULE on
ENV GOPROXY https://goproxy.cn,direct
COPY . /app
WORKDIR /app
CMD [ "./main", "http_start" ]
# 如果只有http服务器或grpc服务器, 请使用如下命令: 
# ENTRYPOINT [ "./main", "http_start" ]
# 如果有多个命令, 你需要编辑./start.sh文件, 选择需要启动的服务, 然后执行如下命令:   
# ENTRYPOINT [ "sh", "./start.sh" ]
# 每个服务都有它向外暴露的端口, 所以当你使用jenkins构建和部署时, 您必须在启动容器时设置更多的端口映射
# 当然如果你部署在k8s集群中, 则需要将端口映射的配置放到deployment文件中
