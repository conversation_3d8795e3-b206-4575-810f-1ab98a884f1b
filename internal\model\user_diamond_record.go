package model

import "time"

type UserDiamondRecord struct {
	Id       int64     `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`                       // 主键
	UserId   int64     `gorm:"column:user_id;NOT NULL" json:"user_id"`                               // 用户id
	OrderId  string    `gorm:"column:order_id;NOT NULL" json:"order_id"`                             // 订单id
	GoodsId  string    `gorm:"column:goods_id;NOT NULL" json:"goods_id"`                             // 商品id
	Diamond  uint64    `gorm:"column:diamond;NOT NULL" json:"diamond"`                               // 钻石变化数额
	Balance  uint64    `gorm:"column:balance;default:0;NOT NULL" json:"balance"`                     // 用户钻石余额
	Type     int       `gorm:"column:type;default:1;NOT NULL" json:"type"`                           // 类型：1增加，-1减少
	Mark     string    `gorm:"column:mark;NOT NULL" json:"mark"`                                     // 备注
	Version  string    `gorm:"column:version;NOT NULL" json:"version"`                               // 版本
	Channel  string    `gorm:"column:channel;NOT NULL" json:"channel"`                               // 客户端/渠道
	CreateAt time.Time `gorm:"column:create_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_at"` // 创建时间
	IsDelete int       `gorm:"column:is_delete;default:-1;NOT NULL" json:"is_delete"`                // 删除标记：-1未删除，1删除
}

func (m *UserDiamondRecord) TableName() string {
	return "user_diamond_record"
}
