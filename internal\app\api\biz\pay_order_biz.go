package biz

import (
	"chongli/component"
	"chongli/component/apollo"
	"chongli/component/driver"
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"chongli/pkg/utils"
	"github.com/panjf2000/ants/v2"
	"time"
)

type PayOrderService struct {
	log            *logger.Logger
	apolloConfig   *apollo.Config
	tx             driver.ITransaction
	antsPool       *ants.Pool
	redisRepo      repo.RedisRepo
	payOrderRepo   repo.PayOrderRepo
	goodsRepo      repo.GoodsVipRepo
	userRepo       repo.UserRepo
	guiyinBindRepo repo.GuiyinDeviceBindRepo
	channelRepo    repo.MarketingChannelRepo
	guiyinService  *service.GuiyinService
}

func NewPayOrderService(
	bootStrap *component.BootStrap,
	guiyinService *service.GuiyinService,
	payOrderRepo repo.PayOrderRepo,
	goodsRepo repo.GoodsVipRepo,
	userRepo repo.UserRepo,
	guiyinBindRepo repo.GuiyinDeviceBindRepo,
	redisRepo repo.RedisRepo,
	channelRepo repo.MarketingChannelRepo,
) *PayOrderService {
	return &PayOrderService{
		apolloConfig:   bootStrap.Config,
		log:            bootStrap.Log,
		tx:             bootStrap.Tx,
		antsPool:       bootStrap.GoroutinePool,
		redisRepo:      redisRepo,
		payOrderRepo:   payOrderRepo,
		goodsRepo:      goodsRepo,
		userRepo:       userRepo,
		guiyinBindRepo: guiyinBindRepo,
		channelRepo:    channelRepo,
		guiyinService:  guiyinService,
	}
}

// CreateOrder 创建订单（下单）
func (s *PayOrderService) CreateOrder(req *dto.CreateOrderRequest) (*dto.CreateOrderResponse, errpkg.IError) {
	// 生成订单ID
	orderID := utils.CreateUUid()

	// 检查订单ID是否已存在
	exists, err := s.payOrderRepo.IsOrderExists(orderID)
	if err != nil {
		s.log.Error("检查订单ID是否存在失败: %v", err)
		return nil, errpkg.NewHighError(response.DbError)
	}
	if exists {
		// 重新生成订单ID
		orderID = utils.CreateUUid()
	}

	mysqlTx := s.tx.MysqlDbTxBegin()

	// 根据 goods_id 查询商品信息
	goods, err := s.goodsRepo.GetGoods(&dto.VipGoodsInfoDto{Id: int(req.GoodsID)}, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		s.log.Error("查询商品信息失败: %v", err)
		return nil, errpkg.NewHighError(response.DbError)
	}
	if goods == nil || goods.Id == 0 {
		mysqlTx.Rollback()
		return nil, errpkg.NewLowError("商品不存在")
	}

	// 根据 device_id 查询用户绑定的渠道id
	channelId, err := s.guiyinService.GetChannelIDByDeviceID(req.UserDeviceId, "")

	// 根据 channel_id 查询绑定渠道信息
	var channel *dto.MarketingChannelDto
	if channelId != 0 {
		channel, err = s.channelRepo.GetMarketingChannelByID(channelId)
		if err != nil {
			mysqlTx.Rollback()
			s.log.Error("查询绑定渠道信息失败: %v", err)
			return nil, errpkg.NewHighError(response.DbError)
		}
	}
	if channel == nil {
		channel = new(dto.MarketingChannelDto)
	}

	// 订单三十分钟过期
	expireAt := time.Now().Add(30 * time.Minute)

	// 创建订单DTO
	orderDto := &dto.PayOrderDto{
		OrderID:             orderID,
		OrderState:          model.OrderStatePending,
		GoodsID:             int64(goods.Id),
		GoodsMiddleID:       goods.GoodsId,
		GoodsTitle:          goods.Title,
		UserID:              req.UserId,
		UserDeviceID:        req.UserDeviceId,
		UserBindChannelID:   int64(channelId),
		UserBindChannelName: channel.Title,
		UserIP:              req.UserIP,
		OrderAmount:         goods.Price,
		Version:             req.Version,
		Channel:             req.Channel,
		ExpireAt:            &expireAt,
		IsDelete:            model.NotDeleted,
	}

	// 保存订单
	if err := s.payOrderRepo.CreatePayOrder(orderDto, mysqlTx); err != nil {
		mysqlTx.Rollback()
		s.log.Error("创建订单失败: %v", err)
		return nil, errpkg.NewHighError(response.DbError)
	}

	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		s.log.Error("提交事务失败: %v", err)
		return nil, errpkg.NewHighError(response.DbError)
	}

	s.log.Info("用户 %d 创建订单成功，订单ID: %s", req.UserId, orderID)

	return &dto.CreateOrderResponse{
		OrderID:       orderID,
		GoodsMiddleID: orderDto.GoodsMiddleID,
		MchCode:       s.apolloConfig.MchCode,
		PartnerId:     s.apolloConfig.PartnerId,
	}, nil
}
