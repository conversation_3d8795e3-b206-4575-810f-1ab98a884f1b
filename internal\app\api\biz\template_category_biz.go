package biz

import (
	"chongli/component"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
)

type TemplateCategoryBiz struct {
	log                  *logger.Logger
	templateCategoryRepo repo.TemplateCategoryRepo
}

func NewTemplateCategoryBiz(
	bootStrap *component.BootStrap,
	templateCategoryRepo repo.TemplateCategoryRepo,
) *TemplateCategoryBiz {
	return &TemplateCategoryBiz{
		log:                  bootStrap.Log,
		templateCategoryRepo: templateCategoryRepo,
	}
}

// GetTemplateCategoryList 获取模板分类列表
func (s *TemplateCategoryBiz) GetTemplateCategoryList(version string, mainClassId int64) ([]*dto.TemplateCategoryDto, errpkg.IError) {
	// 调用数据层获取分类列表，只查询启用且未删除的分类
	categories, err := s.templateCategoryRepo.ListTemplateCategory(version, mainClassId)
	if err != nil {
		s.log.Error("获取模板分类列表失败: %v", err)
		return nil, errpkg.NewHighError(response.DbError)
	}

	return categories, nil
}
