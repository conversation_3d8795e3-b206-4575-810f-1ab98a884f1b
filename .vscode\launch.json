{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch HTTP Server",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": ["http_start"],
            "env": {
                "APOLLO_META": "http://*************:8080/",
                "ENVIRONMENT": "test"
            }
        },
        {
            "name": "Launch Task Worker",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}",
            "args": ["task_start"],
            "env": {
                "APOLLO_META": "http://*************:8080/",
                "ENVIRONMENT": "test"
            }
        }
    ],
    "compounds": [
        {
            "name": "HTTP Server + Task Worker",
            "configurations": ["Launch HTTP Server", "Launch Task Worker"]
        }
    ]
}