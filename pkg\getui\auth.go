package getui

import (
	"chongli/pkg/httpclient"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"strconv"
	"time"
)

/*
token是个推开放平台全局唯一接口调用凭据，访问所有接口都需要此凭据，开发者需要妥善保管。
token的有效期截止时间通过接口返回参数expire_time来标识，目前是接口调用时间+1天的毫秒时间戳。
token过期后无法使用，开发者需要定时刷新。为保证高可用，建议开发者在定时刷新的同时做被动刷新，即当调用业务接口返回错误码10001时调用获取token被动刷新
鉴权接口每分钟最大调用量为100次，每天最大调用量为10万次，建议开发者妥善管理token，以免达到限制，影响推送
*/

type AuthRequest struct {
	Sign      string `json:"sign"`
	TimeStamp string `json:"timestamp"`
	AppKey    string `json:"appkey"`
}

type AuthResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		ExpireTime string `json:"expire_time"`
		Token      string `json:"token"`
	} `json:"data"`
}

func auth() (string, string, error) {
	url := baseUrl + "/auth"
	timeStamp := time.Now().UnixMilli()
	sign := getSign(getuiAppKey, strconv.FormatInt(timeStamp, 10), getuiMasterSecret)
	body, err := json.Marshal(AuthRequest{
		Sign:      sign,
		TimeStamp: strconv.FormatInt(timeStamp, 10),
		AppKey:    getuiAppKey,
	})
	if err != nil {
		return "", "", err
	}
	resp, err := httpclient.HttpPost(url, "", string(body), map[string]string{"Content-Type": "application/json;charset=utf-8"}, 10*time.Second)
	if err != nil {
		return "", "", err
	}
	var respData AuthResponse
	if err := json.Unmarshal([]byte(resp), &respData); err != nil {
		return "", "", err
	}
	return respData.Data.Token, respData.Data.ExpireTime, nil
}

func getSign(appKey, timestamp, masterSecret string) string {
	// 创建一个新的SHA-256哈希实例
	hasher := sha256.New()
	// 向哈希实例中写入数据
	hasher.Write([]byte(appKey + timestamp + masterSecret))
	// 获取哈希值
	hashSum := hasher.Sum(nil)
	return fmt.Sprintf("%x", hashSum)
}
