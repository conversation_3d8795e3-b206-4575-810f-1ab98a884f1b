package registry

import (
	"chongli/component"
	"chongli/internal/app/task/processors"

	"github.com/robfig/cron/v3"
)

// UserTask 用户定时任务管理器
type UserTask struct {
	bootstrap       *component.BootStrap
	userTaskService *processors.UserTaskProcessors
}

func NewUserTask(c *cron.Cron, bootstrap *component.BootStrap, userTaskService *processors.UserTaskProcessors) *UserTask {
	userTask := &UserTask{
		bootstrap:       bootstrap,
		userTaskService: userTaskService,
	}

	// 注册定时任务
	//_, _ = c.AddFunc("0 0 0 * * *", userTask.userTaskService.CheckAndUpdateUserVipStatus) // 每天凌晨十二点检查vip状态
	//_, _ = c.AddFunc("0 0 8 * * *", userTask.userTaskService.GiveDiamondForVip)           // 每天八点赠送会员钻石

	// 测试 每十秒执行一次
	_, _ = c.AddFunc("@every 10s", userTask.userTaskService.CheckAndUpdateUserVipStatus)
	_, _ = c.AddFunc("@every 10s", userTask.userTaskService.GiveDiamondForVip)

	return userTask
}
