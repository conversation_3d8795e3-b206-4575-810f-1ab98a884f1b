package dto

import "time"

// UserInfoDto 用户信息数据传输对象
type UserInfoDto struct {
	ID              int64     `json:"id"`               // 主键id
	DeviceId        string    `json:"device_id"`        // 设备id
	Avatar          string    `json:"avatar"`           // 头像
	Username        string    `json:"username"`         // 用户名
	Phone           string    `json:"phone"`            // 手机号
	Diamond         uint64    `json:"diamond"`          // 钻石
	IsVip           int8      `json:"is_vip"`           // 是否是vip，-1：不是；1：是
	VipType         int8      `json:"vip_type"`         // vip类型，1：月卡；2：季卡；3：年卡
	RegisterType    int64     `json:"register_type"`    // 注册方式，1：一键登录；2：验证码；3：苹果
	RegisterVersion string    `json:"register_version"` // 注册版本
	Channel         string    `json:"channel"`          // 渠道
	Ip              string    `json:"ip"`               // IP地址
	IPLocation      string    `json:"ip_location"`      // IP地理位置
	CreateAt        time.Time `json:"create_at" `       // 创建时间
	UpdateAt        time.Time `json:"update_at" `       // 更新时间
	IsDelete        int8      `json:"is_delete" `       // 是否已被删除，0：未删除；1：已注销

	// guiyin_bind_info
	UserBindChannelId   int64  `json:"user_bind_channel_id"`   // 用户绑定渠道id
	UserBindChannelName string `json:"user_bind_channel_name"` // 用户绑定渠道名称
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	UserOneClickLoginRequest
	UserPhoneCodeLoginRequest
	Type     int    `json:"type"`     // 登录类型 1:一键登录 2:验证码登录 3:苹果登录
	Version  string `json:"version"`  // 版本号
	Channel  string `json:"channel"`  // 渠道
	DeviceId string `json:"deviceId"` // 设备号
	Ip       string `json:"ip"`       // IP地址
}

type UserOneClickLoginRequest struct {
	Token string `json:"token"`
	Gyuid string `json:"gyuid"`
}

type UserPhoneCodeLoginRequest struct {
	Code  string `json:"code"`
	Phone string `json:"phone"`
}

// UserLoginResponse 用户登录响应
type UserLoginResponse struct {
	UserInfo *UserInfo `json:"user_info"`
	Token    string    `json:"token"`
}

type UserInfo struct {
	ID       int64        `json:"id"`
	Avatar   string       `json:"avatar"`   // 头像
	Username string       `json:"username"` // 用户名
	Diamond  uint64       `json:"diamond"`  // 钻石
	Phone    string       `json:"phone"`    // 手机号
	IsVip    int8         `json:"is_vip"`   // 是否是vip，-1：不是；1：是
	VipInfo  *UserVipInfo `json:"vip_info"` // 会员信息
}

// UserSendCodeRequest 发送验证码请求
type UserSendCodeRequest struct {
	Phone     string `json:"phone" binding:"required"`
	Signature string `json:"signature"`
	Timestamp int64  `json:"timestamp"`
}

type GetUserListRequest struct {
	Page                int       `json:"page" form:"page" binding:"required"`                  // 页码
	PageSize            int       `json:"size" form:"size" binding:"required"`                  // 每页数量
	ID                  int64     `json:"id" form:"id"`                                         // 主键id
	DeviceId            string    `json:"device_id" form:"device_id"`                           // 设备id
	UserBindChannelId   int64     `json:"user_bind_channel_id" form:"user_bind_channel_id"`     // 用户绑定渠道id
	UserBindChannelName string    `json:"user_bind_channel_name" form:"user_bind_channel_name"` // 用户绑定渠道名称
	Phone               string    `json:"phone" form:"phone"`                                   // 手机号
	RegisterType        int64     `json:"register_type" form:"register_type"`                   // 注册方式，1：一键登录；2：验证码；3：苹果
	RegisterVersion     string    `json:"register_version" form:"register_version"`             // 注册版本
	Channel             string    `json:"channel" form:"channel"`                               // 渠道
	IsDelete            int8      `json:"is_delete" form:"is_delete"`                           // 是否已被删除，0：未删除；1：已注销
	BeginCreateAt       time.Time `json:"begin_create_at" form:"begin_create_at"`               // 创建时间起始点
	EndCreateAt         time.Time `json:"end_create_at" form:"end_create_at"`                   // 创建时间结束点
	IsVip               int8      `json:"is_vip" form:"is_vip"`                                 // 是否是vip，-1：不是；1：是
	VipType             int8      `json:"vip_type" form:"vip_type"`                             // vip类型，1：月卡；2：季卡；3：年卡
}

type GetUserListResponse struct {
	Total int64          `json:"total"`
	List  []*UserInfoDto `json:"list"`
}

type UpdateUserRequest struct {
	ID         int64  `json:"id" binding:"required"`
	Username   string `json:"username"`
	Avatar     string `json:"avatar"`
	Phone      string `json:"phone"`
	IsDelete   int8   `json:"is_delete"`
	IpLocation string `json:"ip_location"`
	IsVip      int8   `json:"is_vip"`
	VipType    int8   `json:"vip_type"` // vip类型，1：月卡；2：季卡；3：年卡
}
