package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"context"

	"gorm.io/gorm"
)

type taskDAO struct {
	db *gorm.DB
}

func NewTaskRepo(component *component.BootStrap) repo.TaskRepo {
	return &taskDAO{db: component.Driver.GetMysqlDb()}
}

// getDB 根据可选的事务参数返回对应的数据库连接
func (d *taskDAO) getDB(tx ...*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

func (d *taskDAO) Create(ctx context.Context, task *dto.TaskDTO, tx ...*gorm.DB) (*dto.TaskDTO, error) {
	modelTask := &model.Task{
		ID:       task.ID,
		TaskType: task.TaskType,
		Status:   task.Status,
		UserID:   task.UserID,
		DeviceID: task.DeviceID,
	}
	err := d.getDB(tx...).WithContext(ctx).Create(modelTask).Error
	if err != nil {
		return nil, err
	}
	task.ID = modelTask.ID
	return task, nil
}

func (d *taskDAO) GetByID(ctx context.Context, id int64, tx ...*gorm.DB) (*dto.TaskDTO, error) {
	var modelTask model.Task
	err := d.getDB(tx...).WithContext(ctx).Where("id = ?", id).First(&modelTask).Error
	if err != nil {
		return nil, err
	}
	dtoTask := &dto.TaskDTO{
		ID:       modelTask.ID,
		TaskType: modelTask.TaskType,
		Status:   modelTask.Status,
	}
	return dtoTask, nil
}

func (d *taskDAO) Update(ctx context.Context, id int64, updates map[string]interface{}, tx ...*gorm.DB) error {
	return d.getDB(tx...).WithContext(ctx).Model(&model.Task{}).Where("id = ?", id).Updates(updates).Error
}

func (d *taskDAO) Delete(ctx context.Context, id int64, tx ...*gorm.DB) error {
	return d.getDB(tx...).WithContext(ctx).Where("id = ?", id).Delete(&model.Task{}).Error
}
