package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"context"

	"gorm.io/gorm"
)

type userWorkRepo struct {
	log *logger.Logger
	db  *gorm.DB
}

func NewUserWorkRepo(bootStrap *component.BootStrap) repo.UserWorkRepo {
	return &userWorkRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
	}
}

func (d *userWorkRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

func (d *userWorkRepo) Create(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) error {
	data := model.UserWorks{
		UserID:     userWork.UserID,
		TemplateID: userWork.TemplateID,
		WorkType:   userWork.WorkType,
	}
	return d.getDb(tx).Create(&data).Error
}

func (d *userWorkRepo) buildUserWorkQuery(db *gorm.DB, userWork *dto.UserWorks) *gorm.DB {
	query := db.Model(&model.UserWorks{})
	if userWork.UserID != 0 {
		query = query.Where("user_id = ?", userWork.UserID)
	}
	if userWork.WorkType != "" {
		query = query.Where("work_type = ?", userWork.WorkType)
	}
	if userWork.TemplateID != 0 {
		query = query.Where("template_id = ?", userWork.TemplateID)
	}
	if userWork.Status != 0 {
		query = query.Where("status = ?", userWork.Status)
	}
	return query
}

func (d *userWorkRepo) List(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) ([]*dto.UserWorks, error) {
	var works []*dto.UserWorks
	query := d.buildUserWorkQuery(d.getDb(tx), userWork).Preload("User")
	err := query.Find(&works).Error
	return works, err
}

func (d *userWorkRepo) Delete(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) error {
	return d.getDb(tx).Delete(userWork).Error
}

func (d *userWorkRepo) Update(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) error {
	return d.getDb(tx).Updates(userWork).Error
}

func (d *userWorkRepo) Count(ctx context.Context, userWork *dto.UserWorks, tx ...*gorm.DB) (int64, error) {
	var count int64
	query := d.buildUserWorkQuery(d.getDb(tx), userWork)
	err := query.Count(&count).Error
	return count, err
}
