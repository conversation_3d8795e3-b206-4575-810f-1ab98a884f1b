package api

import (
	"chongli/internal/app/api/biz"
	ctrl "chongli/internal/app/api/controller"
	"chongli/internal/app/api/router"
	"chongli/internal/service"

	"github.com/google/wire"
)

// RouterSet 路由依赖注入集合
var RouterSet = wire.NewSet(
	router.NewUserRouter,
	router.NewCommonRouter,
	router.NewTemplateRouter,
	router.NewBusinessRouter,
)

// ControllerSet 控制器依赖注入集合
var ControllerSet = wire.NewSet(
	ctrl.NewUserLoginController,
	ctrl.NewUserInfoController,
	ctrl.NewCommonController,
	ctrl.NewTemplateCategoryController,
	ctrl.NewGoodsController,
	ctrl.NewUserWorksController,
	ctrl.NewPayOrderController,
	ctrl.NewPayNotifyController,
)

// ServiceSet 服务层依赖注入集合
var ServiceSet = wire.NewSet(
	biz.NewUserLoginService,
	biz.NewGuiyinService,
	biz.NewUserInfoBiz,
	biz.NewConfigBiz,
	biz.NewPopupBiz,
	biz.NewTemplateCategoryBiz,
	biz.NewGoodsBiz,
	biz.NewPayOrderService,
	service.NewPayNotifyService,
	biz.NewUserWorkBiz,
)
