package model

// StatusFlag 状态标识类型
type StatusFlag int8

// 通用状态常量 (使用 1 和 -1)
const (
	StatusDisabled StatusFlag = -1 // 禁用/失败/未使用/未删除
	StatusEnabled  StatusFlag = 1  // 启用/成功/已使用/已删除
)

// 验证码用途常量
const (
	VerifyCodePurposeLogin    = 1 // 登录
	VerifyCodePurposeResetPwd = 2 // 重置密码
	VerifyCodePurposeOther    = 3 // 其他
)

// 验证码相关常量
const (
	VerifyCodeLength    = 6  // 验证码长度
	VerifyCodeExpireMin = 5  // 验证码过期时间（分钟）
	SendCodeCooldownSec = 60 // 发送验证码冷却时间（秒）
	MaxLoginAttempts    = 5  // 最大登录尝试次数
	LoginLockMinutes    = 30 // 登录锁定时间（分钟）
)

// Redis Key 前缀
const (
	RedisKeyVerifyCode    = "admin:verify_code:"    // 验证码缓存key
	RedisKeySendCooldown  = "admin:send_cooldown:"  // 发送冷却key
	RedisKeyLoginAttempts = "admin:login_attempts:" // 登录尝试次数key
	RedisKeyLoginLock     = "admin:login_lock:"     // 登录锁定key
)

// 邮件模板相关
const (
	EmailTemplateLogin = "admin_login" // 登录邮件模板
	EmailSubjectLogin  = "后台管理系统登录验证码" // 登录邮件主题
)

// 兼容性配置常量
const (
	// AdminUserStatusCompatMode 管理员用户状态字段兼容模式
	// true: 在响应中包含status字段（向后兼容）
	// false: 不包含status字段（新版本）
	AdminUserStatusCompatMode = true

	// AdminUserStatusCompatValue 兼容模式下status字段的默认值
	AdminUserStatusCompatValue = StatusEnabled // 始终返回启用状态
)

const (
	DefaultChannelId = 1
)
