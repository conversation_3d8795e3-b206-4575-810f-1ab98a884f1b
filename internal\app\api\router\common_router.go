package router

import (
	"chongli/internal/app/api/controller"

	"github.com/gin-gonic/gin"
)

type CommonRouter struct {
	commonCtrl *controller.CommonController
}

func NewCommonRouter(engine *gin.Engine, commonCtrl *controller.CommonController) *CommonRouter {

	router := &CommonRouter{commonCtrl: commonCtrl}

	engine.POST("api/guiyin/bind", commonCtrl.BindAttributeData) // 绑定归因数据

	engine.GET("api/config", commonCtrl.ConfigList) // 获取配置

	engine.GET("api/version/", commonCtrl.GetVersion) // 获取版本

	engine.GET("api/popup/list", commonCtrl.Popup) // 获取弹窗

	return router
}
