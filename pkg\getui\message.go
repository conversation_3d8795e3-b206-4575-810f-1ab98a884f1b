package getui

// Transmission 消息体
type Transmission struct {
	Type    string `json:"type"` // 消息类型
	Payload any    `json:"data"` // 消息载荷
	Msg     string `json:"msg"`  // 消息内容
	Time    int64  `json:"time"` // 消息时间戳
}

// MakeSongPayload 歌曲制作消息载荷
type MakeSongPayload struct {
	SongID   int64  `json:"song_id"`   // 歌曲id
	State    int    `json:"state"`     // 歌曲制作结果;1:制作成功;2:制作失败
	ErrMsg   string `json:"err_msg"`   // 歌曲制作失败时携带的原因
	Title    string `json:"title"`     // 歌曲标题
	ImgURL   string `json:"img_url"`   // 歌曲封面图片地址
	CreateAt int64  `json:"create_at"` // 歌曲制作时间
}

// MakeMvPayload MV制作消息载荷
type MakeMvPayload struct {
	MvID     int64  `json:"mv_id"`     // MV id
	State    int    `json:"state"`     // MV 制作结果;1:制作成功;2:制作失败
	ErrMsg   string `json:"err_msg"`   // MV 制作失败时携带的原因
	Title    string `json:"title"`     // MV标题
	ImgURL   string `json:"img_url"`   // MV封面图片地址
	CreateAt int64  `json:"create_at"` // MV制作时间
}
