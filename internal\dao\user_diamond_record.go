package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type userDiamondRecordRepo struct {
	log *logger.Logger
	db  *gorm.DB
	rds *redis.Client
}

func NewUserDiamondRecordRepo(bootStrap *component.BootStrap) repo.UserDiamondRecordRepo {
	return &userDiamondRecordRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
		rds: bootStrap.Driver.GetRdsClient(),
	}
}

func (d *userDiamondRecordRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

// CreateUserDiamondRecord 创建用户钻石记录
func (d *userDiamondRecordRepo) CreateUserDiamondRecord(record *dto.UserDiamondRecordDto, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.UserDiamondRecord{}).Create(record).Error; err != nil {
		d.log.Error("创建用户钻石记录错误: %v", err.Error())
		return err
	}
	return nil
}

// DeleteUserDiamondRecord 删除用户钻石记录（软删除）
func (d *userDiamondRecordRepo) DeleteUserDiamondRecord(id int64) error {
	if err := d.db.Model(&model.UserDiamondRecord{}).Where("id = ?", id).Update("is_delete", model.StatusEnabled).Error; err != nil {
		d.log.Error("删除用户钻石记录错误: %v", err.Error())
		return err
	}
	return nil
}

// Page 分页查询用户钻石记录
func (d *userDiamondRecordRepo) Page(req *dto.UserDiamondRecordPageRequest) ([]*dto.UserDiamondRecordDto, int64, error) {
	var records []*model.UserDiamondRecord
	var total int64
	query := d.db.Model(&model.UserDiamondRecord{}).Where("is_delete = ?", model.StatusDisabled)

	// 构建查询条件
	if req.UserId != 0 {
		query = query.Where("user_id = ?", req.UserId)
	}

	if req.OrderId != "" {
		query = query.Where("order_id = ?", req.OrderId)
	}

	if req.GoodsId != "" {
		query = query.Where("goods_id = ?", req.GoodsId)
	}

	if req.Type != 0 {
		query = query.Where("type = ?", req.Type)
	}

	if req.Channel != "" {
		query = query.Where("channel = ?", req.Channel)
	}

	if req.Version != "" {
		query = query.Where("version = ?", req.Version)
	}

	if !req.BeginAt.IsZero() {
		query = query.Where("create_at >= ?", req.BeginAt)
	}

	if !req.EndAt.IsZero() {
		query = query.Where("create_at <= ?", req.EndAt)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		d.log.Error("查询用户钻石记录总数失败: %v", err)
		return nil, 0, err
	}

	// 获取列表
	offset := (req.Page - 1) * req.PageSize
	err = query.Offset(offset).Limit(req.PageSize).Order("create_at DESC").Find(&records).Error
	if err != nil {
		d.log.Error("查询用户钻石记录列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为DTO
	var dtoList []*dto.UserDiamondRecordDto
	for _, record := range records {
		dtoList = append(dtoList, d.convertToDto(record))
	}

	return dtoList, total, nil
}

// convertToDto 转换为DTO
func (d *userDiamondRecordRepo) convertToDto(record *model.UserDiamondRecord) *dto.UserDiamondRecordDto {
	return &dto.UserDiamondRecordDto{
		Id:       record.Id,
		UserId:   record.UserId,
		OrderId:  record.OrderId,
		GoodsId:  record.GoodsId,
		Diamond:  record.Diamond,
		Balance:  record.Balance,
		Type:     record.Type,
		Mark:     record.Mark,
		Version:  record.Version,
		Channel:  record.Channel,
		CreateAt: record.CreateAt,
		IsDelete: record.IsDelete,
	}
}
