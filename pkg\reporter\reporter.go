package reporter

import (
	"chongli/pkg/utils"
	"encoding/json"
	"fmt"
	"time"

	"chongli/component/apollo"
	"chongli/pkg/logger"
	"gitlab.ylwnl.com/middleware/dmp.51wnl-cq.com-sdk/consts"
	"gitlab.ylwnl.com/middleware/dmp.51wnl-cq.com-sdk/reporter"
	"gitlab.ylwnl.com/middleware/dmp.51wnl-cq.com-sdk/reporter/dto"
)

func InitReporter() {
	// 初始化SDK, 初始化一次就行, SDK内部使用单例模式.
	err := reporter.InitReporter(dto.Base{
		AppID: "jianyinhezi",                                      // appid由数据中台创建
		Env:   consts.Env(apollo.GetApolloConfig().DataReportEnv), // 环境, 支持测试环境和正式环境
		Pkg:   "com.youloft.musicbos",                             // 包名
	},
		consts.HTTPNetworkProtocol, // 网络协议, 目前仅支持HTTP协议
		consts.JSONTransProtocol,   // 传输协议, 支持JSON和Protobuf两种传输协议, 建议使用Protobuf协议
		3*time.Second,              // 连接超时时间, 体量小建议设置为3s, 体量大建议设置为5s
	)
	if err != nil {
		panic(err)
	}
}

type ReportData struct {
	// 必填
	AdDeviceID   string // 归因成功后响应, 后续上报事件携带ad_device_id和active_expire会提升上报效率
	ActiveExpire int64  // ms时间戳
	ActionTime   int64  // 客户端实际行为时间
	Client       string // 苹果设备: "ios"; 安卓设备: "android"
	// 选填
	OS          string // 操作系统
	IP          string // ip 原值
	UA          string // user-agent 原值
	Model       string // 设备类型
	ThirdUserID string // 应用内用户 id
	DistinctID  string // 数数 distinct_id
	YouLoftDID  string // 扶摇访客 id
	// 安卓选填
	OaidMD5      string // oaid原值加密后的小写值
	AndroidIDMd5 string // android_id原值加密后的小写值
	ImeiMD5      string // imei原值MD5后的小写值
	// 苹果选填
	IdfaMD5 string // idfa原值加密后的小写值
}

type OrderData struct {
	OrderID     string
	OrderAmount float64
	PayAt       int64
	UserID      int64
}

type GoodsData struct {
	GoodsID   string
	GoodsName string
}

type UserData struct {
	UserId       int64
	DeviceId     string
	RegisterTime int64
	IP           string
}

// ParseReportData 解析 JSON 格式的上报数据
func ParseReportData(reportDataJson, channel string) (*ReportData, error) {
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(reportDataJson), &result); err != nil {
		logger.Log().Error("解析上报数据失败: %v", err)
		return nil, err
	}

	reportData := &ReportData{
		AdDeviceID:   getStringFromMap(result, "ad_device_id"),
		ActiveExpire: getInt64FromMap(result, "active_expire"),
		ImeiMD5:      getStringFromMap(result, "imei_md5"),
		OaidMD5:      getStringFromMap(result, "oaid_md5"),
		AndroidIDMd5: getStringFromMap(result, "android_id_md5"),
		IdfaMD5:      getStringFromMap(result, "idfa_md5"),
		IP:           getStringFromMap(result, "ip"),
		UA:           getStringFromMap(result, "ua"),
		Model:        getStringFromMap(result, "model"),
		OS:           getStringFromMap(result, "os"),
		Client:       getStringFromMap(result, "client"),
		ThirdUserID:  getStringFromMap(result, "third_user_id"),
		DistinctID:   getStringFromMap(result, "distinct_id"),
		YouLoftDID:   getStringFromMap(result, "youloft_did"),
		ActionTime:   getInt64FromMap(result, "action_time"),
	}

	// 临时解决方法 如果没有 从请求头中获取 channel 转为 client
	var err error
	if reportData.Client == "" {
		reportData.Client, err = utils.ClearPlatform(channel)
		if err != nil {
			logger.Log().Error("从请求头解析出client出错: %v", err.Error())
			return nil, err
		}
	}

	return reportData, nil
}

// 辅助函数：从 map[string]interface{} 获取字符串值
func getStringFromMap(m map[string]interface{}, key string) string {
	if val, ok := m[key]; ok {
		if strVal, ok := val.(string); ok {
			return strVal
		}
	}
	return ""
}

// 辅助函数：从 map[string]interface{} 获取 int64 值
func getInt64FromMap(m map[string]interface{}, key string) int64 {
	if val, ok := m[key]; ok {
		switch v := val.(type) {
		case float64:
			return int64(v)
		case int64:
			return v
		case int:
			return int64(v)
		default:
			return 0
		}
	}
	return 0
}

func setDevice(reportData *ReportData) dto.DeviceData {
	return dto.DeviceData{
		AdDeviceID:   reportData.AdDeviceID,
		ActiveExpire: reportData.ActiveExpire,
		ImeiMD5:      reportData.ImeiMD5,
		OaidMD5:      reportData.OaidMD5,
		AndroidIDMd5: reportData.AndroidIDMd5,
		IdfaMD5:      reportData.IdfaMD5,
		IP:           reportData.IP,
		UA:           reportData.UA,
		Model:        reportData.Model,
		OS:           reportData.OS,
		Client:       reportData.Client,
		ThirdUserID:  reportData.ThirdUserID,
		DistinctID:   reportData.DistinctID,
		YouLoftDID:   reportData.YouLoftDID,
	}
}

func setUser(payDetail *dto.PayDetail, behaviorDetail *dto.BehaviorDetail, actionTime int64) dto.User {
	user := dto.User{
		ActionTime: actionTime,
	}
	if payDetail != nil {
		user.PayDetail = *payDetail
	}
	if behaviorDetail != nil {
		user.BehaviorDetail = *behaviorDetail
	}

	return user
}

func checkResp(resp dto.ReportResponseDto, err error) error {
	if err != nil {
		logger.Log().Error("归因上报失败: %v", err)
		return err
	}
	if resp.Code != 200 {
		logger.Log().Error("归因上报失败: %v", resp.Msg)
		return fmt.Errorf("归因上报失败: %v", resp.Msg)
	}
	return nil
}

func ReportPaySuccess(reportData *ReportData, orderData *OrderData, goodsData *GoodsData) error {
	device := setDevice(reportData)

	user := setUser(&dto.PayDetail{
		OrderAmount: orderData.OrderAmount,
		OrderStatus: 2,
		GoodID:      goodsData.GoodsID,
		GoodName:    goodsData.GoodsName,
		OrderID:     orderData.OrderID,
	}, nil, orderData.PayAt)

	err := checkResp(reporter.ReportToDMPV2(consts.EventTypePay, []dto.DeviceData{device}, user, nil, nil))
	if err != nil {
		return err
	}

	logger.Log().Info("归因上报成功支付事件成功, reportData: %+v, orderData: %+v, goodsData: %+v", reportData, orderData, goodsData)

	return nil
}

func ReportRegister(reportData *ReportData, userData *UserData) error {
	device := setDevice(reportData)

	user := setUser(nil, nil, userData.RegisterTime)

	err := checkResp(reporter.ReportToDMPV2(consts.EventTypeRegister, []dto.DeviceData{device}, user, nil, nil))
	if err != nil {
		return err
	}

	logger.Log().Info("归因上报注册事件成功, reportData: %+v, userData: %+v", reportData, userData)

	return nil
}
