package model

import "time"

// PopupModel -
type PopupModel struct {
	ID         int       `gorm:"column:id;primary_key;AUTO_INCREMENT" json:"id"`
	Location   string    `gorm:"column:location;type:varchar(32);NOT NULL" json:"location"`
	Jump       string    `gorm:"column:jump;type:varchar(128);NOT NULL" json:"jump"`
	JumpParam  string    `gorm:"column:jump_param;type:varchar(255);NOT NULL" json:"jump_param"`
	PopupImg   string    `gorm:"column:popup_img;type:varchar(255)" json:"popup_img"`
	PopupVideo string    `gorm:"column:popup_video;type:varchar(255)" json:"popup_video"`
	IsDelete   int8      `gorm:"column:is_delete;type:enum('1','-1');NOT NULL;default:-1" json:"is_delete"`
	CreateAt   time.Time `gorm:"column:create_at;default:CURRENT_TIMESTAMP;NOT NULL" json:"create_at"`
	UpdateAt   time.Time `gorm:"column:update_at;default:CURRENT_TIMESTAMP;NOT NULL;autoUpdateTime" json:"update_at"`
	Title      string    `gorm:"column:title;type:varchar(128)" json:"title"`
	Content    string    `gorm:"column:content;type:varchar(255)" json:"content"`
}

// TableName -
func (p *PopupModel) TableName() string {
	return "popup"
}
