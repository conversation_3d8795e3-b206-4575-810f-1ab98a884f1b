package router

import (
	"chongli/internal/app/admin/controller"
	"chongli/internal/middleware"
	"github.com/gin-gonic/gin"
)

type TemplateManageRouter struct {
	templateCtrl *controller.TemplateManageController
}

// NewTemplateManageRouter 创建模板管理路由
func NewTemplateManageRouter(engine *gin.Engine, templateCtrl *controller.TemplateManageController) *TemplateManageRouter {
	router := &TemplateManageRouter{templateCtrl: templateCtrl}

	// 模板管理相关路由（需要JWT验证）
	templateManage := engine.Group("/admin/template")
	templateManage.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		// 模板分类管理
		category := templateManage.Group("/category")
		{
			category.GET("list", router.templateCtrl.GetTemplateCategoryList)                // 分页条件查询模板分类
			category.POST("create", router.templateCtrl.CreateTemplateCategory)              // 创建模板分类
			category.PUT("update", router.templateCtrl.UpdateTemplateCategory)               // 更新模板分类
			category.DELETE("batch_delete", router.templateCtrl.BatchDeleteTemplateCategory) // 批量删除模板分类
			category.PUT("batch_update", router.templateCtrl.BatchUpdateTemplateCategory)    // 批量更新模板分类
			category.GET("main_class/list", router.templateCtrl.GetMainClassList)            // 获取主分类列表
		}
	}

	return router
}
