package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"errors"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

type marketingChannelRepo struct {
	log *logger.Logger
	db  *gorm.DB
	rds *redis.Client
}

// NewMarketingChannelRepo 创建营销渠道仓库实例
func NewMarketingChannelRepo(bootStrap *component.BootStrap) repo.MarketingChannelRepo {
	return &marketingChannelRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
		rds: bootStrap.Driver.GetRdsClient(),
	}
}

// CreateMarketingChannel 创建营销渠道
func (r *marketingChannelRepo) CreateMarketingChannel(channel *dto.MarketingChannelCreateDto) (*dto.MarketingChannelDto, error) {
	// 转换为model
	modelChannel := &model.MarketingChannel{
		Type:      channel.Type,
		Title:     channel.Title,
		BindKey:   channel.BindKey,
		BindValue: channel.BindValue,
		IsDeleted: int8(channel.IsDeleted),
	}

	// 创建记录
	err := r.db.Create(modelChannel).Error
	if err != nil {
		r.log.Error("创建营销渠道失败: %v", err)
		return nil, err
	}

	// 转换并返回DTO
	return r.convertToDto(modelChannel), nil
}

// GetMarketingChannelByID 根据ID获取营销渠道
func (r *marketingChannelRepo) GetMarketingChannelByID(id int) (*dto.MarketingChannelDto, error) {
	var channel model.MarketingChannel
	err := r.db.Where("id = ?", id).First(&channel).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		r.log.Error("根据ID查询营销渠道失败: %v", err)
		return nil, err
	}

	return r.convertToDto(&channel), nil
}

// GetMarketingChannel 根据绑定键和绑定值获取营销渠道（单条查询，过滤已删除）
func (r *marketingChannelRepo) GetMarketingChannel(query *dto.MarketingChannelQueryDto) (*dto.MarketingChannelDto, error) {
	var channel model.MarketingChannel

	// 构建查询条件，确保包含未删除条件
	queryBuilder := r.db.Where("bind_key = ? AND bind_value = ? AND is_deleted = ?",
		query.BindKey, query.BindValue, model.StatusDisabled)

	err := queryBuilder.First(&channel).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		r.log.Error("根据绑定键和绑定值查询营销渠道失败: bindKey=%s, bindValue=%s, err=%v",
			query.BindKey, query.BindValue, err)
		return nil, err
	}

	return r.convertToDto(&channel), nil
}

// GetMarketingChannelList 获取营销渠道列表
func (r *marketingChannelRepo) GetMarketingChannelList(query *dto.MarketingChannelQueryDto) ([]*dto.MarketingChannelDto, error) {
	// 设置默认分页参数
	if query.Page <= 0 {
		query.Page = 1
	}
	if query.PageSize <= 0 {
		query.PageSize = 10
	}

	// 构建查询条件
	condition := r.buildQueryCondition(query)
	offset := (query.Page - 1) * query.PageSize

	// 获取列表
	channels, _, err := r.GetMarketingChannelListByConditionWithQuery(condition, query, offset, query.PageSize)
	if err != nil {
		return nil, err
	}

	return channels, nil
}

// GetMarketingChannelCount 获取营销渠道总数
func (r *marketingChannelRepo) GetMarketingChannelCount(query *dto.MarketingChannelQueryDto) (int64, error) {
	// 构建查询条件
	condition := r.buildQueryCondition(query)

	var total int64
	queryBuilder := r.db.Model(&model.MarketingChannel{})

	// 添加查询条件
	if condition != nil {
		queryBuilder = queryBuilder.Where(condition)
	}

	// 添加标题模糊匹配
	queryBuilder = r.buildQueryWithTitle(queryBuilder, query)

	// 获取总数
	err := queryBuilder.Count(&total).Error
	if err != nil {
		r.log.Error("查询营销渠道总数失败: %v", err)
		return 0, err
	}

	return total, nil
}

// UpdateMarketingChannel 更新营销渠道
func (r *marketingChannelRepo) UpdateMarketingChannel(channel *dto.MarketingChannelUpdateDto) (*dto.MarketingChannelDto, error) {
	// 检查记录是否存在
	existingChannel, err := r.GetMarketingChannelByID(channel.ID)
	if err != nil {
		return nil, err
	}
	if existingChannel == nil {
		return nil, fmt.Errorf("营销渠道不存在，ID: %d", channel.ID)
	}

	// 构建更新数据
	updates := map[string]interface{}{
		"type":       channel.Type,
		"title":      channel.Title,
		"bind_key":   channel.BindKey,
		"bind_value": channel.BindValue,
		"is_deleted": channel.IsDeleted,
		"update_at":  time.Now(),
	}

	// 执行更新
	err = r.db.Model(&model.MarketingChannel{}).Where("id = ?", channel.ID).Updates(updates).Error
	if err != nil {
		r.log.Error("更新营销渠道失败: %v", err)
		return nil, err
	}

	// 返回更新后的记录
	return r.GetMarketingChannelByID(channel.ID)
}

// DeleteMarketingChannel 删除营销渠道（软删除）
func (r *marketingChannelRepo) DeleteMarketingChannel(id int) error {
	err := r.db.Model(&model.MarketingChannel{}).Where("id = ?", id).Update("is_deleted", model.StatusEnabled).Error
	if err != nil {
		r.log.Error("删除营销渠道失败: %v", err)
		return err
	}
	return nil
}

// GetMarketingChannelListByCondition 根据条件获取营销渠道列表
func (r *marketingChannelRepo) GetMarketingChannelListByConditionWithQuery(condition map[string]interface{}, queryDto *dto.MarketingChannelQueryDto, offset, limit int) ([]*dto.MarketingChannelDto, int64, error) {
	var channels []*model.MarketingChannel
	var total int64

	query := r.db.Model(&model.MarketingChannel{})

	// 添加查询条件
	if condition != nil {
		query = query.Where(condition)
	}

	// 添加标题模糊匹配
	if queryDto != nil {
		query = r.buildQueryWithTitle(query, queryDto)
	}

	// 获取总数
	err := query.Count(&total).Error
	if err != nil {
		r.log.Error("查询营销渠道总数失败: %v", err)
		return nil, 0, err
	}

	// 获取列表
	err = query.Offset(offset).Limit(limit).Order("id DESC").Find(&channels).Error
	if err != nil {
		r.log.Error("查询营销渠道列表失败: %v", err)
		return nil, 0, err
	}

	// 转换为DTO
	var channelDtos []*dto.MarketingChannelDto
	for _, channel := range channels {
		channelDtos = append(channelDtos, r.convertToDto(channel))
	}

	return channelDtos, total, nil
}

// buildQueryCondition 构建查询条件
func (r *marketingChannelRepo) buildQueryCondition(query *dto.MarketingChannelQueryDto) map[string]interface{} {
	condition := make(map[string]interface{})

	// 类型筛选
	if query.Type != nil {
		condition["type"] = *query.Type
	}

	// 绑定键筛选
	if query.BindKey != "" {
		condition["bind_key"] = query.BindKey
	}

	// 绑定值筛选
	if query.BindValue != "" {
		condition["bind_value"] = query.BindValue
	}

	// 删除状态筛选
	if query.IsDeleted != nil {
		condition["is_deleted"] = *query.IsDeleted
	}

	return condition
}

// buildQueryWithTitle 构建包含标题模糊匹配的查询
func (r *marketingChannelRepo) buildQueryWithTitle(baseQuery *gorm.DB, query *dto.MarketingChannelQueryDto) *gorm.DB {
	// 标题模糊匹配
	if query.Title != "" {
		baseQuery = baseQuery.Where("title LIKE ?", "%"+query.Title+"%")
	}
	return baseQuery
}

// convertToDto 将model转换为dto
func (r *marketingChannelRepo) convertToDto(channel *model.MarketingChannel) *dto.MarketingChannelDto {
	if channel == nil {
		return nil
	}

	return &dto.MarketingChannelDto{
		ID:        channel.ID,
		Type:      channel.Type,
		Title:     channel.Title,
		BindKey:   channel.BindKey,
		BindValue: channel.BindValue,
		IsDeleted: channel.IsDeleted,
		CreateAt:  channel.CreateAt,
		UpdateAt:  channel.UpdateAt,
	}
}
