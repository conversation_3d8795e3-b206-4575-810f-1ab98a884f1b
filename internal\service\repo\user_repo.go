package repo

import (
	"chongli/internal/service/dto"

	"gorm.io/gorm"
)

// UserRepo 用户数据仓库接口
type UserRepo interface {
	// CreateUser 创建用户
	CreateUser(user *dto.UserInfoDto) error
	// GetUserInfoByDeviceId 根据 device_id 查询用户
	GetUserInfoByDeviceId(deviceId string) (*dto.UserInfoDto, error)
	// UpdateUsername 更新用户名
	UpdateUsername(userId int64, username string) error
	// UpdateUserInfoByDeviceId 根据 device_id 更新用户信息
	UpdateUserInfoByDeviceId(deviceId string, field map[string]any) error
	// UpdateUserInfoByUserId 根据 user_id 更新用户信息
	UpdateUserInfoByUserId(userId int64, field map[string]any, tx ...*gorm.DB) error
	// GetUserInfoByField 根据字段获取用户信息
	GetUserInfoByField(field, data string) (*dto.UserInfoDto, error)
	// IsNewUser 判断用户是否是新用户
	IsNewUser(field, data string) (bool, error)
	// IsCancelUser 判断用户是否已注销
	IsCancelUser(field, data string) (bool, error)
	// SetSmsCode 保存短信验证码
	SetSmsCode(phone, code string) (err error)
	// GetSmsCode 获取短信验证码
	GetSmsCode(phone string) (code string, err error)
	// GetSendCooling 获取发送冷却时间
	GetSendCooling(string) (bool, error)
	// SetSendCooling 设置发送冷却时间
	SetSendCooling(string) error
	// SetPhoneError 设置手机错误次数
	SetPhoneError(phone string) error
	// GetPhoneError 获取手机错误次数
	GetPhoneError(phone string) (int64, error)
	// GetUserInfoByUserId 根据 user_id 获取用户信息
	GetUserInfoByUserId(userId int64, tx ...*gorm.DB) (*dto.UserInfoDto, error)
	// DeleteUserByUserId 根据 user_id 删除用户
	DeleteUserByUserId(userId int64) error
	// UncancelUserByUserId 根据 user_id 恢复用户
	UncancelUserByUserId(userId int64) error
	// Page 分页获取用户列表
	Page(req *dto.GetUserListRequest) ([]*dto.UserInfoDto, int64, error)
	// GetAllVipUsers 获取所有VIP用户
	GetAllVipUsers() ([]*dto.UserInfoDto, error)
	// BatchUpdateUserInfoByIds 批量更新用户信息
	BatchUpdateUserInfoByIds(ids []int64, field map[string]any) error
}
