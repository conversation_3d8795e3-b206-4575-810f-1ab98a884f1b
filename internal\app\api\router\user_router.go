package router

import (
	"chongli/internal/app/api/controller"
	"chongli/internal/middleware"
	"github.com/gin-gonic/gin"
)

type UserRouter struct {
	userLoginCtrl *controller.UserLoginController
	userInfoCtrl  *controller.UserInfoController
}

func NewUserRouter(
	engine *gin.Engine,
	userLoginCtrl *controller.UserLoginController,
	userInfoCtrl *controller.UserInfoController,
) *UserRouter {
	router := &UserRouter{
		userLoginCtrl: userLoginCtrl,
		userInfoCtrl:  userInfoCtrl,
	}
	user := engine.Group("api/user/login")
	router.initLogin(user)
	userInfo := engine.Group("api/user/info")
	router.initUserInfo(userInfo)
	return router
}

func (r *UserRouter) initLogin(user *gin.RouterGroup) {
	// 一键登录
	user.POST("phone", r.userLoginCtrl.OneClickLogin)
	// 发送验证码
	user.POST("send_code", r.userLoginCtrl.SendCode)
	// 验证码登录
	user.POST("code", r.userLoginCtrl.PhoneCodeLogin)
	// 苹果登录
	user.GET("apple", r.userLoginCtrl.AppleLogin)
}

func (r *UserRouter) initUserInfo(userInfo *gin.RouterGroup) {
	userInfo.Use(middleware.JWTAuth())
	// 获取用户信息
	userInfo.GET("", r.userInfoCtrl.GetUserInfo)
	// 用户注销
	userInfo.POST("cancel", r.userInfoCtrl.CancelUser)
}
