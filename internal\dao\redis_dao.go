package dao

import (
	"chongli/component"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"context"
	"errors"
	"time"

	"github.com/go-redis/redis/v8"
)

type redisRepo struct {
	log *logger.Logger
	rds *redis.Client
}

// NewRedisRepo 创建 Redis 仓库实例
func NewRedisRepo(bootStrap *component.BootStrap) repo.RedisRepo {
	return &redisRepo{
		log: bootStrap.Log,
		rds: bootStrap.Driver.GetRdsClient(),
	}
}

// Get 从 Redis 获取值
func (r *redisRepo) Get(ctx context.Context, key string) (string, error) {
	result := r.rds.Get(ctx, key)
	if errors.Is(result.Err(), redis.Nil) {
		return "", nil // key 不存在，返回空字符串
	}
	if result.Err() != nil {
		r.log.Error("Redis Get 操作失败, key: %s, err: %v", key, result.Err())
		return "", result.Err()
	}
	return result.Val(), nil
}

// Set 设置值到 Redis
func (r *redisRepo) Set(ctx context.Context, key string, value string, expiration time.Duration) error {
	err := r.rds.Set(ctx, key, value, expiration).Err()
	if err != nil {
		r.log.Error("Redis Set 操作失败, key: %s, value: %s, expiration: %v, err: %v", key, value, expiration, err)
		return err
	}
	return nil
}

// Del 从 Redis 删除值
func (r *redisRepo) Del(ctx context.Context, key string) error {
	err := r.rds.Del(ctx, key).Err()
	if err != nil {
		r.log.Error("Redis Del 操作失败, key: %s, err: %v", key, err)
		return err
	}
	return nil
}

// Exists 检查 key 是否存在
func (r *redisRepo) Exists(ctx context.Context, key string) (bool, error) {
	result := r.rds.Exists(ctx, key)
	if result.Err() != nil {
		r.log.Error("Redis Exists 操作失败, key: %s, err: %v", key, result.Err())
		return false, result.Err()
	}
	return result.Val() > 0, nil
}
