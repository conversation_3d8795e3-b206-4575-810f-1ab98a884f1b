package processors

import (
	"chongli/component"
	"chongli/internal/service"
	"context"
	"fmt"
)

type PicTaskProcessors struct {
	bootstrap   *component.BootStrap
	taskService *service.TaskService
}

// NewPicTaskProcessors 创建图片任务处理器实例
func NewPicTaskProcessors(
	bootstrap *component.BootStrap,
	taskService *service.TaskService,
) *PicTaskProcessors {
	return &PicTaskProcessors{
		bootstrap:   bootstrap,
		taskService: taskService,
	}
}

// 第一步
func (s *PicTaskProcessors) AiCompound() {
	tx := s.bootstrap.Tx.MysqlDbTxBegin()
	step, err := s.taskService.GetPendingTaskStep(context.Background(), "ai_compound", tx)
	if err != nil {
		tx.Rollback()
		return
	}

	fmt.Println(step)
	tx.Commit()
}
