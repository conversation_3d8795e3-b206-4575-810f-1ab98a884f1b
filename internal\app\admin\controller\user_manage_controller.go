package controller

import (
	"chongli/component"
	"chongli/component/driver"
	"chongli/internal/model"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
	"github.com/gin-gonic/gin"
)

type UserManageController struct {
	log         *logger.Logger
	tx          driver.ITransaction
	userService *service.UserService
	userRepo    repo.UserRepo
	userVipRepo repo.UserVipRepo
	recordRepo  repo.UserDiamondRecordRepo
}

func NewUserManageController(
	bootStrap *component.BootStrap,
	userService *service.UserService,
	userRepo repo.UserRepo,
	userVipRepo repo.UserVipRepo,
	recordRepo repo.UserDiamondRecordRepo,
) *UserManageController {
	return &UserManageController{
		log:         bootStrap.Log,
		tx:          bootStrap.Tx,
		userService: userService,
		userRepo:    userRepo,
		userVipRepo: userVipRepo,
		recordRepo:  recordRepo,
	}
}

// UserList 获取用户列表
func (c *UserManageController) UserList(ctx *gin.Context) {
	var req dto.GetUserListRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	list, total, err := c.userRepo.Page(&req)
	if err != nil {
		c.log.Error("获取用户列表失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
	}

	response.Response(ctx, nil, &dto.GetUserListResponse{
		Total: total,
		List:  list,
	}, nil, response.WithSLSLog)
}

// UserDetail 获取用户详情
func (c *UserManageController) UserDetail(ctx *gin.Context) {
	type Request struct {
		ID int64 `json:"id" binding:"required"`
	}
	var req Request
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	userInfo, err := c.userRepo.GetUserInfoByUserId(req.ID)
	if err != nil {
		c.log.Error("获取用户详情失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
	}

	response.Response(ctx, nil, userInfo, nil, response.WithSLSLog)
}

func (c *UserManageController) UserVipInfo(ctx *gin.Context) {
	type Request struct {
		UserID int64 `json:"user_id" form:"user_id" binding:"required"`
	}
	var req Request
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	vipInfo, err := c.userVipRepo.GetUserVipByUserId(req.UserID)
	if err != nil {
		c.log.Error("获取用户VIP信息失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
	}

	response.Response(ctx, nil, vipInfo, nil, response.WithSLSLog)
}

// UserEdit 编辑用户信息
func (c *UserManageController) UserEdit(ctx *gin.Context) {
	var req dto.UpdateUserRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	err := c.userService.UpdateUser(&req, nil)
	response.Response(ctx, nil, nil, err, response.WithSLSLog)
}

func (c *UserManageController) DiamondRecordList(ctx *gin.Context) {
	var req dto.UserDiamondRecordPageRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	list, total, err := c.recordRepo.Page(&req)
	if err != nil {
		c.log.Error("获取用户钻石记录列表失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
	}
	response.Response(ctx, nil, &dto.UserDiamondRecordPageResponse{
		Total: total,
		List:  list,
	}, nil, response.WithSLSLog)
}

func (c *UserManageController) AddUserDiamond(ctx *gin.Context) {
	var req dto.AddUserDiamondRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	mysqlTx := c.tx.MysqlDbTxBegin()
	err := c.userService.AddOrSubUserDiamondAndAddRecord(service.DiamondOperationRequest{
		UserID:  req.UserId,
		Diamond: req.Diamond,
		Mark:    req.Mark,
		Version: req.Version,
		Channel: req.Channel,
		Op:      int(model.StatusEnabled),
		OrderID: "管理后台",
		GoodsID: "管理后台",
	}, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		c.log.Error("增加用户钻石失败: %v", err)
		return
	}
	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		c.log.Error("提交事务失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}

func (c *UserManageController) OpenUserVip(ctx *gin.Context) {
	var req dto.CreateAndRenewUserVipRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	req.GoodsID, req.OrderID = "管理后台", "管理后台"

	mysqlTx := c.tx.MysqlDbTxBegin()
	_, err := c.userService.OpenVip(&req, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		c.log.Error("开通用户VIP失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}
	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		c.log.Error("提交事务失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}

func (c *UserManageController) RenewUserVip(ctx *gin.Context) {
	var req dto.CreateAndRenewUserVipRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	req.GoodsID, req.OrderID = "管理后台", "管理后台"

	mysqlTx := c.tx.MysqlDbTxBegin()
	err := c.userService.RenewVip(&req, mysqlTx)
	if err != nil {
		mysqlTx.Rollback()
		c.log.Error("续费用户VIP失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}
	if err := mysqlTx.Commit().Error; err != nil {
		mysqlTx.Rollback()
		c.log.Error("提交事务失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, nil, nil, response.WithSLSLog)
}

func (c *UserManageController) VipGiveRecordList(ctx *gin.Context) {
	var req dto.VipGivePageRequest
	if err := ctx.ShouldBindQuery(&req); err != nil {
		response.Response(ctx, nil, nil, errpkg.NewLowError(err.Error()), response.WithSLSLog)
		return
	}

	resp, err := c.userVipRepo.PageVipGive(&req)
	if err != nil {
		c.log.Error("获取VIP赠送记录失败: %v", err)
		response.Response(ctx, nil, nil, errpkg.NewHighError(response.DbError), response.WithSLSLog)
		return
	}
	response.Response(ctx, nil, resp, nil, response.WithSLSLog)
}
