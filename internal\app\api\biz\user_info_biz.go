package biz

import (
	"chongli/component"
	"chongli/internal/service"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	errpkg "chongli/pkg/error"
	"chongli/pkg/logger"
	"chongli/pkg/response"
)

type UserInfoBiz struct {
	log         *logger.Logger
	userService *service.UserService
	userRepo    repo.UserRepo
	userVipRepo repo.UserVipRepo
}

func NewUserInfoBiz(
	bootstrap *component.BootStrap,
	userService *service.UserService,
	userRepo repo.UserRepo,
	userVipRepo repo.UserVipRepo,
) *UserInfoBiz {
	return &UserInfoBiz{
		log:         bootstrap.Log,
		userService: userService,
		userRepo:    userRepo,
		userVipRepo: userVipRepo,
	}
}

// GetUserInfo 获取用户信息
func (s *UserInfoBiz) GetUserInfo(userId int64) (*dto.UserInfo, errpkg.IError) {
	// 获取用户详细信息
	userInfoDto, err := s.userRepo.GetUserInfoByUserId(userId)
	if err != nil {
		s.log.Error("获取用户信息失败: %v", err)
		return nil, errpkg.NewHighError(response.DbError)
	}

	// 检查用户是否存在
	if userInfoDto.ID == 0 {
		return nil, errpkg.NewLowError("用户不存在")
	}

	// 检查用户是否已注销
	if userInfoDto.IsDelete == 1 {
		return nil, errpkg.NewLowError("用户已注销")
	}

	// 转换为简化的用户信息
	userInfo := &dto.UserInfo{
		ID:       userInfoDto.ID,
		Avatar:   userInfoDto.Avatar,
		Username: userInfoDto.Username,
		Diamond:  userInfoDto.Diamond,
		Phone:    s.handlePhone(userInfoDto.Phone),
		IsVip:    userInfoDto.IsVip,
		VipInfo:  nil,
	}

	if userInfoDto.IsVip == 1 {
		// 获取会员信息
		vipInfo, err := s.userVipRepo.GetUserVipByUserId(userId)
		if err != nil {
			s.log.Error("获取会员信息失败: %v", err)
			return nil, errpkg.NewHighError(response.DbError)
		}
		userInfo.VipInfo = &dto.UserVipInfo{
			VipType:   vipInfo.VipType,
			CreateAt:  &vipInfo.CreateAt,
			RenewalAt: vipInfo.RenewalAt,
			ExpireAt:  &vipInfo.ExpireAt,
			IsExpire:  vipInfo.IsExpire,
		}
	}

	return userInfo, nil
}

// 手机号处理，将中间几位用*代替
func (s *UserInfoBiz) handlePhone(phone string) string {
	if phone != "" && len(phone) == 11 {
		return phone[:3] + "****" + phone[7:]
	} else {
		return "暂未绑定手机号"
	}
}
