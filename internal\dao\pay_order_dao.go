package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"errors"
	"time"

	"gorm.io/gorm"
)

type payOrderRepo struct {
	log *logger.Logger
	db  *gorm.DB
}

func NewPayOrderRepo(bootStrap *component.BootStrap) repo.PayOrderRepo {
	return &payOrderRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
	}
}

func (d *payOrderRepo) getDb(tx []*gorm.DB) *gorm.DB {
	if len(tx) > 0 && tx[0] != nil {
		return tx[0]
	}
	return d.db
}

// convertModel2Dto 将模型转换为DTO
func (d *payOrderRepo) convertModel2Dto(order *model.PayOrder) *dto.PayOrderDto {
	return &dto.PayOrderDto{
		ID:                  order.ID,
		OrderID:             order.OrderID,
		WnlOrderID:          order.WnlOrderID,
		OrderState:          order.OrderState,
		OrderStateText:      order.GetOrderStateText(),
		GoodsID:             order.GoodsID,
		GoodsMiddleID:       order.GoodsMiddleID,
		GoodsTitle:          order.GoodsTitle,
		UserID:              order.UserID,
		UserDeviceID:        order.UserDeviceID,
		UserBindChannelID:   order.UserBindChannelID,
		UserBindChannelName: order.UserBindChannelName,
		UserIP:              order.UserIP,
		PayType:             order.PayType,
		PayTypeText:         order.GetPayTypeText(),
		OrderAmount:         order.OrderAmount,
		PayAmount:           order.PayAmount,
		RefundAmount:        order.RefundAmount,
		Version:             order.Version,
		Channel:             order.Channel,
		PayAt:               order.PayAt,
		RefundAt:            order.RefundAt,
		ExpireAt:            order.ExpireAt,
		CreateAt:            order.CreateAt,
		UpdateAt:            order.UpdateAt,
		WnlCallbackData:     order.WnlCallbackData,
		Remark:              order.Remark,
		IsDelete:            order.IsDelete,
	}
}

// convertDto2Model 将DTO转换为模型
func (d *payOrderRepo) convertDto2Model(dto *dto.PayOrderDto) *model.PayOrder {
	return &model.PayOrder{
		ID:                  dto.ID,
		OrderID:             dto.OrderID,
		WnlOrderID:          dto.WnlOrderID,
		OrderState:          dto.OrderState,
		GoodsID:             dto.GoodsID,
		GoodsMiddleID:       dto.GoodsMiddleID,
		GoodsTitle:          dto.GoodsTitle,
		UserID:              dto.UserID,
		UserDeviceID:        dto.UserDeviceID,
		UserBindChannelID:   dto.UserBindChannelID,
		UserBindChannelName: dto.UserBindChannelName,
		UserIP:              dto.UserIP,
		PayType:             dto.PayType,
		OrderAmount:         dto.OrderAmount,
		PayAmount:           dto.PayAmount,
		RefundAmount:        dto.RefundAmount,
		Version:             dto.Version,
		Channel:             dto.Channel,
		PayAt:               dto.PayAt,
		RefundAt:            dto.RefundAt,
		ExpireAt:            dto.ExpireAt,
		CreateAt:            dto.CreateAt,
		UpdateAt:            dto.UpdateAt,
		WnlCallbackData:     dto.WnlCallbackData,
		Remark:              dto.Remark,
		IsDelete:            dto.IsDelete,
	}
}

// CreatePayOrder 创建支付订单
func (d *payOrderRepo) CreatePayOrder(orderDto *dto.PayOrderDto, tx ...*gorm.DB) error {
	order := d.convertDto2Model(orderDto)
	if err := d.getDb(tx).Create(order).Error; err != nil {
		d.log.Error("创建支付订单错误: %v", err.Error())
		return err
	}
	// 更新DTO中的ID
	orderDto.ID = order.ID
	return nil
}

// GetPayOrderByID 根据ID获取支付订单
func (d *payOrderRepo) GetPayOrderByID(id int64, tx ...*gorm.DB) (*dto.PayOrderDto, error) {
	var order model.PayOrder
	if err := d.getDb(tx).Where("id = ? AND is_delete = ?", id, model.NotDeleted).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据ID获取支付订单错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&order), nil
}

// GetPayOrderByOrderID 根据订单ID获取支付订单
func (d *payOrderRepo) GetPayOrderByOrderID(orderID string, tx ...*gorm.DB) (*dto.PayOrderDto, error) {
	var order model.PayOrder
	if err := d.getDb(tx).Where("order_id = ? AND is_delete = ?", orderID, model.NotDeleted).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据订单ID获取支付订单错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&order), nil
}

// GetPayOrderByWnlOrderID 根据万年历订单ID获取支付订单
func (d *payOrderRepo) GetPayOrderByWnlOrderID(wnlOrderID string, tx ...*gorm.DB) (*dto.PayOrderDto, error) {
	var order model.PayOrder
	if err := d.getDb(tx).Where("wnl_order_id = ? AND is_delete = ?", wnlOrderID, model.NotDeleted).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据万年历订单ID获取支付订单错误: %v", err.Error())
		return nil, err
	}
	return d.convertModel2Dto(&order), nil
}

// UpdatePayOrder 更新支付订单
func (d *payOrderRepo) UpdatePayOrder(orderDto *dto.PayOrderDto, tx ...*gorm.DB) error {
	order := d.convertDto2Model(orderDto)
	if err := d.getDb(tx).Save(order).Error; err != nil {
		d.log.Error("更新支付订单错误: %v", err.Error())
		return err
	}
	return nil
}

// UpdatePayOrderByID 根据ID更新支付订单
func (d *payOrderRepo) UpdatePayOrderByID(id int64, fields map[string]any, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("id = ? AND is_delete = ?", id, model.NotDeleted).Updates(fields).Error; err != nil {
		d.log.Error("根据ID更新支付订单错误: %v", err.Error())
		return err
	}
	return nil
}

// UpdatePayOrderByOrderID 根据订单ID更新支付订单
func (d *payOrderRepo) UpdatePayOrderByOrderID(orderID string, fields map[string]any, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("order_id = ? AND is_delete = ?", orderID, model.NotDeleted).Updates(fields).Error; err != nil {
		d.log.Error("根据订单ID更新支付订单错误: %v", err.Error())
		return err
	}
	return nil
}

// DeletePayOrder 删除支付订单（软删除）
func (d *payOrderRepo) DeletePayOrder(id int64, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("id = ?", id).Update("is_delete", model.Deleted).Error; err != nil {
		d.log.Error("删除支付订单错误: %v", err.Error())
		return err
	}
	return nil
}

// DeletePayOrderByOrderID 根据订单ID删除支付订单（软删除）
func (d *payOrderRepo) DeletePayOrderByOrderID(orderID string, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("order_id = ?", orderID).Update("is_delete", model.Deleted).Error; err != nil {
		d.log.Error("根据订单ID删除支付订单错误: %v", err.Error())
		return err
	}
	return nil
}

// GetPayOrderList 获取支付订单列表
func (d *payOrderRepo) GetPayOrderList(req *dto.GetPayOrderListRequest) ([]*dto.PayOrderDto, int64, error) {
	var orders []*model.PayOrder
	var total int64

	query := d.db.Model(&model.PayOrder{})

	// 构建查询条件
	if req.OrderID != "" {
		query = query.Where("order_id LIKE ?", "%"+req.OrderID+"%")
	}
	if req.WnlOrderID != "" {
		query = query.Where("wnl_order_id LIKE ?", "%"+req.WnlOrderID+"%")
	}
	if req.OrderState != nil && *req.OrderState != 0 {
		query = query.Where("order_state = ?", *req.OrderState)
	}
	if req.GoodsID != 0 {
		query = query.Where("goods_id = ?", req.GoodsID)
	}
	if req.GoodsMiddleID != "" {
		query = query.Where("goods_middle_id = ?", req.GoodsMiddleID)
	}
	if req.UserID != nil && *req.UserID != 0 {
		query = query.Where("user_id = ?", *req.UserID)
	}
	if req.UserDeviceID != "" {
		query = query.Where("user_device_id = ?", req.UserDeviceID)
	}
	if req.UserBindChannelID != 0 {
		query = query.Where("user_bind_channel_id = ?", req.UserBindChannelID)
	}
	if req.UserBindChannelName != "" {
		query = query.Where("user_bind_channel_name = ?", req.UserBindChannelName)
	}
	if req.UserIP != "" {
		query = query.Where("user_ip = ?", req.UserIP)
	}
	if req.PayType != nil && *req.PayType != 0 {
		query = query.Where("pay_type = ?", *req.PayType)
	}
	if req.Version != "" {
		query = query.Where("version = ?", req.Version)
	}
	if req.Channel != "" {
		query = query.Where("channel = ?", req.Channel)
	}
	if req.IsDelete != nil && *req.IsDelete != 0 {
		query = query.Where("is_delete = ?", *req.IsDelete)
	}
	if req.BeginCreateAt != nil && !req.BeginCreateAt.IsZero() {
		query = query.Where("create_at >= ?", *req.BeginCreateAt)
	}
	if req.EndCreateAt != nil && !req.BeginCreateAt.IsZero() {
		query = query.Where("create_at <= ?", *req.EndCreateAt)
	}
	if req.BeginPayAt != nil && !req.BeginCreateAt.IsZero() {
		query = query.Where("pay_at >= ?", *req.BeginPayAt)
	}
	if req.EndPayAt != nil && !req.BeginCreateAt.IsZero() {
		query = query.Where("pay_at <= ?", *req.EndPayAt)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		d.log.Error("获取支付订单总数错误: %v", err.Error())
		return nil, 0, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := query.Order("create_at DESC").Offset(offset).Limit(req.PageSize).Find(&orders).Error; err != nil {
		d.log.Error("获取支付订单列表错误: %v", err.Error())
		return nil, 0, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}

	return orderDtos, total, nil
}

// GetPayOrdersByUserID 根据用户ID获取支付订单列表
func (d *payOrderRepo) GetPayOrdersByUserID(userID int64, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("user_id = ? AND is_delete = ?", userID, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据用户ID获取支付订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetPayOrdersByGoodsID 根据商品ID获取支付订单列表
func (d *payOrderRepo) GetPayOrdersByGoodsID(goodsID string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("goods_id = ? AND is_delete = ?", goodsID, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据商品ID获取支付订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetPayOrdersByState 根据订单状态获取支付订单列表
func (d *payOrderRepo) GetPayOrdersByState(state int, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("order_state = ? AND is_delete = ?", state, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据订单状态获取支付订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetExpiredOrders 获取过期订单列表
func (d *payOrderRepo) GetExpiredOrders(limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	now := time.Now()
	query := d.getDb(tx).Where("(order_state = ? OR expire_at < ?) AND is_delete = ?", model.OrderStateExpired, now, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("获取过期订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetPendingOrders 获取待支付订单列表
func (d *payOrderRepo) GetPendingOrders(limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	return d.GetPayOrdersByState(model.OrderStatePending, limit, tx...)
}

// GetPaidOrders 获取已支付订单列表
func (d *payOrderRepo) GetPaidOrders(limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	return d.GetPayOrdersByState(model.OrderStatePaid, limit, tx...)
}

// CountOrdersByUserID 统计用户订单数量
func (d *payOrderRepo) CountOrdersByUserID(userID int64, tx ...*gorm.DB) (int64, error) {
	var count int64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("user_id = ? AND is_delete = ?", userID, model.NotDeleted).Count(&count).Error; err != nil {
		d.log.Error("统计用户订单数量错误: %v", err.Error())
		return 0, err
	}
	return count, nil
}

// CountOrdersByState 统计指定状态的订单数量
func (d *payOrderRepo) CountOrdersByState(state int, tx ...*gorm.DB) (int64, error) {
	var count int64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("order_state = ? AND is_delete = ?", state, model.NotDeleted).Count(&count).Error; err != nil {
		d.log.Error("统计指定状态的订单数量错误: %v", err.Error())
		return 0, err
	}
	return count, nil
}

// CountOrdersByPayType 统计指定支付方式的订单数量
func (d *payOrderRepo) CountOrdersByPayType(payType int, tx ...*gorm.DB) (int64, error) {
	var count int64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("pay_type = ? AND is_delete = ?", payType, model.NotDeleted).Count(&count).Error; err != nil {
		d.log.Error("统计指定支付方式的订单数量错误: %v", err.Error())
		return 0, err
	}
	return count, nil
}

// GetOrderAmountSumByUserID 获取用户订单总金额
func (d *payOrderRepo) GetOrderAmountSumByUserID(userID int64, tx ...*gorm.DB) (float64, error) {
	var sum float64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("user_id = ? AND is_delete = ?", userID, model.NotDeleted).Select("COALESCE(SUM(order_amount), 0)").Scan(&sum).Error; err != nil {
		d.log.Error("获取用户订单总金额错误: %v", err.Error())
		return 0, err
	}
	return sum, nil
}

// GetPayAmountSumByUserID 获取用户实际支付总金额
func (d *payOrderRepo) GetPayAmountSumByUserID(userID int64, tx ...*gorm.DB) (float64, error) {
	var sum float64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("user_id = ? AND order_state = ? AND is_delete = ?", userID, model.OrderStatePaid, model.NotDeleted).Select("COALESCE(SUM(pay_amount), 0)").Scan(&sum).Error; err != nil {
		d.log.Error("获取用户实际支付总金额错误: %v", err.Error())
		return 0, err
	}
	return sum, nil
}

// GetRefundAmountSumByUserID 获取用户退款总金额
func (d *payOrderRepo) GetRefundAmountSumByUserID(userID int64, tx ...*gorm.DB) (float64, error) {
	var sum float64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("user_id = ? AND order_state = ? AND is_delete = ?", userID, model.OrderStateRefund, model.NotDeleted).Select("COALESCE(SUM(refund_amount), 0)").Scan(&sum).Error; err != nil {
		d.log.Error("获取用户退款总金额错误: %v", err.Error())
		return 0, err
	}
	return sum, nil
}

// BatchUpdateOrderState 批量更新订单状态
func (d *payOrderRepo) BatchUpdateOrderState(orderIDs []string, state int, tx ...*gorm.DB) error {
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("order_id IN ? AND is_delete = ?", orderIDs, model.NotDeleted).Update("order_state", state).Error; err != nil {
		d.log.Error("批量更新订单状态错误: %v", err.Error())
		return err
	}
	return nil
}

// GetOrdersByChannel 根据渠道获取订单列表
func (d *payOrderRepo) GetOrdersByChannel(channel string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("channel = ? AND is_delete = ?", channel, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据渠道获取订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetOrdersByVersion 根据版本获取订单列表
func (d *payOrderRepo) GetOrdersByVersion(version string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("version = ? AND is_delete = ?", version, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据版本获取订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetOrdersByUserIP 根据用户IP获取订单列表
func (d *payOrderRepo) GetOrdersByUserIP(userIP string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("user_ip = ? AND is_delete = ?", userIP, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据用户IP获取订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetOrdersByUserDeviceID 根据用户设备ID获取订单列表
func (d *payOrderRepo) GetOrdersByUserDeviceID(userDeviceID string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("user_device_id = ? AND is_delete = ?", userDeviceID, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据用户设备ID获取订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// GetOrdersByUserBindChannelID 根据用户绑定渠道ID获取订单列表
func (d *payOrderRepo) GetOrdersByUserBindChannelID(userBindChannelID string, limit int, tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	query := d.getDb(tx).Where("user_bind_channel_id = ? AND is_delete = ?", userBindChannelID, model.NotDeleted).Order("create_at DESC")
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("根据用户绑定渠道ID获取订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// IsOrderExists 检查订单是否存在
func (d *payOrderRepo) IsOrderExists(orderID string, tx ...*gorm.DB) (bool, error) {
	var count int64
	if err := d.getDb(tx).Model(&model.PayOrder{}).
		Where("order_id = ? AND is_delete = ?", orderID, model.NotDeleted).
		Count(&count).Error; err != nil {
		d.log.Error("检查订单是否存在错误: %v", err.Error())
		return false, err
	}
	return count > 0, nil
}

// IsWnlOrderExists 检查万年历订单是否存在
func (d *payOrderRepo) IsWnlOrderExists(wnlOrderID string, tx ...*gorm.DB) (bool, error) {
	var count int64
	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("wnl_order_id = ? AND is_delete = ?", wnlOrderID, model.NotDeleted).Count(&count).Error; err != nil {
		d.log.Error("检查万年历订单是否存在错误: %v", err.Error())
		return false, err
	}
	return count > 0, nil
}

// GetExpiredUnpaidOrders 获取过期未支付订单列表
func (d *payOrderRepo) GetExpiredUnpaidOrders(tx ...*gorm.DB) ([]*dto.PayOrderDto, error) {
	var orders []*model.PayOrder
	now := time.Now()
	query := d.getDb(tx).Where("order_state = ? AND expire_at < ? AND is_delete = ?", model.OrderStatePending, now, model.NotDeleted).Order("create_at DESC")

	if err := query.Find(&orders).Error; err != nil {
		d.log.Error("获取过期未支付订单列表错误: %v", err.Error())
		return nil, err
	}

	// 转换为DTO
	var orderDtos []*dto.PayOrderDto
	for _, order := range orders {
		orderDtos = append(orderDtos, d.convertModel2Dto(order))
	}
	return orderDtos, nil
}

// BatchUpdateOrderStateAndRemark 批量更新订单状态和备注
func (d *payOrderRepo) BatchUpdateOrderStateAndRemark(orderIDs []string, state int, remark string, tx ...*gorm.DB) error {
	if len(orderIDs) == 0 {
		return nil
	}

	updates := map[string]any{
		"order_state": state,
		"remark":      remark,
		"update_at":   time.Now(),
	}

	if err := d.getDb(tx).Model(&model.PayOrder{}).Where("order_id IN ? AND is_delete = ?", orderIDs, model.NotDeleted).Updates(updates).Error; err != nil {
		d.log.Error("批量更新订单状态和备注错误: %v", err.Error())
		return err
	}
	return nil
}
