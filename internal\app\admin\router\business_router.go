package router

import (
	"chongli/internal/app/admin/controller"
	"chongli/internal/middleware"

	"github.com/gin-gonic/gin"
)

type BusinessRouter struct {
	goodsVipChannelCtrl *controller.GoodsController
	goodsBaseCtrl       *controller.GoodsBaseController
	orderManageCtrl     *controller.PayOrderManageController
}

// NewBusinessRouter 创建业务相关路由
func NewBusinessRouter(
	engine *gin.Engine,
	goodsVipChannelCtrl *controller.GoodsController,
	goodsBaseCtrl *controller.GoodsBaseController,
	orderManageCtrl *controller.PayOrderManageController,
) *BusinessRouter {
	router := &BusinessRouter{
		goodsVipChannelCtrl: goodsVipChannelCtrl,
		goodsBaseCtrl:       goodsBaseCtrl,
		orderManageCtrl:     orderManageCtrl,
	}

	// 支付管理相关路由（需要JWT验证）
	payManage := engine.Group("/admin/pay")
	payManage.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		// 订单管理
		payManage.GET("order/list", router.orderManageCtrl.OrderList)         // 获取订单列表
		payManage.GET("order/detail/:id", router.orderManageCtrl.OrderDetail) // 获取订单详情
		payManage.GET("order/refund", router.orderManageCtrl.OrderRefund)     // 订单退款
	}

	// 商品VIP渠道相关路由（需要JWT验证）
	business := engine.Group("/admin/business")
	business.Use(middleware.AdminJWTAuth()) // 添加JWT中间件
	{
		business.GET("/goods/list", router.goodsVipChannelCtrl.List)                 // 获取商品VIP渠道列表
		business.POST("/goods/add", router.goodsVipChannelCtrl.Add)                  // 添加商品VIP渠道
		business.GET("/goods/delete", router.goodsVipChannelCtrl.Delete)             // 删除商品VIP渠道
		business.POST("/goods/batch-delete", router.goodsVipChannelCtrl.BatchDelete) // 批量删除商品VIP渠道

		business.GET("/goods_base/list", router.goodsBaseCtrl.List)  // 获取基础商品
		business.POST("/goods_base/add", router.goodsBaseCtrl.Add)   // 添加基础商品
		business.POST("/goods_base/edit", router.goodsBaseCtrl.Edit) // 编辑基础商品
	}

	return router
}
