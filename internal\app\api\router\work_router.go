package router

// #region 所有和作品相关的路由

import (
	"chongli/internal/app/api/controller"
	"chongli/internal/middleware"

	"github.com/gin-gonic/gin"
)

type TemplateRouter struct {
	templateCategoryCtrl *controller.TemplateCategoryController
	userWorkCtrl         *controller.UserWorksController
}

func NewTemplateRouter(
	engine *gin.Engine,
	templateCategoryCtrl *controller.TemplateCategoryController,
	userWorkCtrl *controller.UserWorksController,
) *TemplateRouter {
	router := &TemplateRouter{
		templateCategoryCtrl: templateCategoryCtrl,
		userWorkCtrl:         userWorkCtrl,
	}
	template := engine.Group("api/template")
	router.initTemplateCategory(template)
	work := engine.Group("api/work")
	work.Use(middleware.JWTAuth())
	router.initWork(work)
	return router
}

func (r *TemplateRouter) initTemplateCategory(template *gin.RouterGroup) {
	category := template.Group("category")
	// 获取模板分类列表
	category.GET("list/:id", r.templateCategoryCtrl.GetTemplateCategoryList)
}

func (r *TemplateRouter) initWork(work *gin.RouterGroup) {

	work.POST("make", r.userWorkCtrl.CreatePicWorks)
}
