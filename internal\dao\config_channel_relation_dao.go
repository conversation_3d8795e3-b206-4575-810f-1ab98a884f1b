package dao

import (
	"chongli/component"
	"chongli/internal/model"
	"chongli/internal/service/dto"
	"chongli/internal/service/repo"
	"chongli/pkg/logger"
	"errors"

	"github.com/go-redis/redis/v8"
	"gorm.io/gorm"
)

// configChannelRelationRepo 配置渠道关联数据仓库实现
type configChannelRelationRepo struct {
	log *logger.Logger
	db  *gorm.DB
	rds *redis.Client
}

// NewConfigChannelRelationRepo 创建配置渠道关联数据仓库实例
func NewConfigChannelRelationRepo(bootStrap *component.BootStrap) repo.ConfigChannelRelationRepo {
	return &configChannelRelationRepo{
		log: bootStrap.Log,
		db:  bootStrap.Driver.GetMysqlDb(),
		rds: bootStrap.Driver.GetRdsClient(),
	}
}

// List 获取配置渠道关联列表，支持多种条件筛选
func (d *configChannelRelationRepo) List(query *dto.ConfigChannelRelationDto) ([]*dto.ConfigChannelRelationDto, error) {
	var relations []*model.ConfigChannelRelation
	
	// 构建基本查询条件
	db := d.db.Preload("Config").Preload("MarketingChannel")
	
	// 根据配置ID筛选
	if query.ConfigID > 0 {
		db = db.Where("config_channel_relation.config_id = ?", query.ConfigID)
	}
	
	// 根据渠道ID筛选
	if query.MarketingChannelID > 0 {
		db = db.Where("config_channel_relation.marketing_channel_id = ?", query.MarketingChannelID)
	}
	
	// 构建基本连接条件
	db = db.Joins("JOIN config ON config.id = config_channel_relation.config_id")
	db = db.Joins("JOIN channel ON channel.id = config_channel_relation.marketing_channel_id")
	
	// 添加配置筛选条件
	if query.Config != nil {
		if query.Config.ConfigKey != "" {
			db = db.Where("config.config_key LIKE ?", "%"+query.Config.ConfigKey+"%")
		}
		if query.Config.IsDelete != 0 {
			db = db.Where("config.is_delete = ?", int8(query.Config.IsDelete))
		}
		if query.Config.IsChannel != 0 {
			db = db.Where("config.is_channel = ?", int8(query.Config.IsChannel))
		}
		if query.Config.IsPublish != 0 {
			db = db.Where("config.is_publish = ?", int8(query.Config.IsPublish))
		}
	}
	
	// 添加营销渠道删除状态筛选
	if query.MarketingChannel != nil && query.MarketingChannel.IsDeleted != 0 {
		db = db.Where("channel.is_deleted = ?", query.MarketingChannel.IsDeleted)
	}
	
	// 分页处理
	if query.Page > 0 && query.PageSize > 0 {
		offset := (query.Page - 1) * query.PageSize
		db = db.Offset(offset).Limit(query.PageSize)
	}
	
	// 执行查询
	err := db.Find(&relations).Error
	
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据条件查询配置关联失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var result []*dto.ConfigChannelRelationDto
	for _, relation := range relations {
		result = append(result, d.convertToDto(relation))
	}

	return result, nil
}

// CreateConfigChannelRelation 创建配置渠道关联
func (d *configChannelRelationRepo) CreateConfigChannelRelation(configID int, channelID int) error {
	relation := &model.ConfigChannelRelation{
		ConfigID:           configID,
		MarketingChannelID: channelID,
	}

	err := d.db.Create(relation).Error
	if err != nil {
		d.log.Error("创建配置渠道关联失败: %v", err)
		return err
	}

	return nil
}

// DeleteConfigChannelRelation 删除配置渠道关联
func (d *configChannelRelationRepo) DeleteConfigChannelRelation(id int) error {
	err := d.db.Where("id = ?", id).
		Delete(&model.ConfigChannelRelation{}).Error
	
	if err != nil {
		d.log.Error("删除配置渠道关联失败: %v", err)
		return err
	}

	return nil
}

// GetChannelsByConfigID 根据配置ID获取关联的渠道列表
func (d *configChannelRelationRepo) GetChannelsByConfigID(configID int) ([]*dto.ConfigChannelRelationDto, error) {
	var relations []*model.ConfigChannelRelation
	
	// 使用预加载查询关联的配置和渠道信息
	err := d.db.Preload("Config").
		Preload("MarketingChannel").
		Where("config_id = ?", configID).
		// 只查询未删除的渠道
		Joins("JOIN channel ON channel.id = config_channel_relation.channel_id AND channel.is_deleted = ?", 
			model.StatusDisabled).
		Find(&relations).Error
	
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		d.log.Error("根据配置ID查询渠道关联失败: %v", err)
		return nil, err
	}

	// 转换为DTO
	var result []*dto.ConfigChannelRelationDto
	for _, relation := range relations {
		result = append(result, d.convertToDto(relation))
	}

	return result, nil
}

// convertToDto 将模型转换为DTO
func (d *configChannelRelationRepo) convertToDto(relation *model.ConfigChannelRelation) *dto.ConfigChannelRelationDto {
	if relation == nil {
		return nil
	}

	result := &dto.ConfigChannelRelationDto{
		ID:                 relation.ID,
		ConfigID:           relation.ConfigID,
		MarketingChannelID: relation.MarketingChannelID,
		CreateAt:           relation.CreateAt,
	}

	// 如果预加载了配置信息，则转换配置DTO
	if relation.Config.Id > 0 {
		result.Config = &dto.ConfigDto{
			Id:          relation.Config.Id,
			ConfigKey:   relation.Config.ConfigKey,
			ConfigValue: relation.Config.ConfigValue,
			Remark:      relation.Config.Remark,
			CreateAt:    relation.Config.CreateAt,
			IsDelete:    model.StatusFlag(relation.Config.IsDelete),
			IsChannel:   model.StatusFlag(relation.Config.IsChannel),
			IsPublish:   model.StatusFlag(relation.Config.IsPublish),
		}
	}

	// 如果预加载了渠道信息，则设置渠道信息
	if relation.MarketingChannel.ID > 0 {
		// 直接使用关联的营销渠道信息
		result.MarketingChannel = &relation.MarketingChannel
	}

	return result
}
